<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gold Price Widget</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      background: transparent;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      overflow: hidden;
      -webkit-app-region: drag;
      user-select: none;
    }
    
    #app {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .widget-container {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      transition: transform 0.2s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    
    .widget-container:hover {
      transform: scale(1.05);
    }
    
    .price-display {
      color: white;
      font-size: 11px;
      font-weight: bold;
      text-align: center;
      line-height: 1.1;
    }
    
    .price-value {
      font-size: 12px;
      margin-bottom: 2px;
    }
    
    .price-unit {
      font-size: 8px;
      opacity: 0.8;
    }
    
    .price-change {
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 10px;
    }
    
    .loading {
      color: white;
      font-size: 12px;
      animation: pulse 1.5s ease-in-out infinite;
    }
    
    .error {
      color: #ff6b6b;
      font-size: 10px;
    }
    
    .price-up {
      color: #2ecc71;
    }
    
    .price-down {
      color: #e74c3c;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  </style>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/main.js"></script>
</body>
</html>
