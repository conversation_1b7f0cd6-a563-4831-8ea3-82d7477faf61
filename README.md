# Gold Price Widget

A cross-platform desktop widget for real-time gold price monitoring built with Electron and Vue 3.

## Features

- **Floating Ball Widget**: A circular, always-on-top widget that displays current gold prices
- **Real-time Updates**: Automatic price updates every 30 seconds (configurable)
- **Price Change Indicators**: Visual indicators for price increases (green ↑) and decreases (red ↓)
- **System Tray Integration**: Minimize to system tray with context menu
- **Position Persistence**: Remembers widget position across application restarts
- **Settings Window**: Configure update frequency and other preferences
- **Cross-platform**: Works on Windows, macOS, and Linux

## Installation

### Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd gold-price-widget
```

2. Install dependencies:
```bash
npm install
```

3. Run in development mode:
```bash
npm run electron-dev
```

### Building for Production

Build the Vue app and run Electron:
```bash
npm run build
npm run electron
```

### Creating Distributables

Build for all platforms:
```bash
npm run dist
```

Build for specific platforms:
```bash
npm run dist:win    # Windows (.exe)
npm run dist:mac    # macOS (.dmg)
npm run dist:linux  # Linux (.AppImage)
```

## Usage

1. **Widget Display**: The widget appears as a circular floating ball showing the current gold price in USD per ounce
2. **Dragging**: Click and drag the widget to move it around your screen
3. **System Tray**: Right-click the system tray icon to access options:
   - Show/Hide Widget
   - Settings
   - Exit Application
4. **Settings**: Configure the price update interval (5-300 seconds)

## Technical Details

### Architecture

- **Main Process**: Handles window management, system tray, and configuration persistence
- **Renderer Process**: Vue 3 application for the UI
- **IPC Communication**: Secure communication between main and renderer processes

### API Integration

Currently uses a mock API for demonstration. To integrate with a real gold price API:

1. Update the `API_ENDPOINT` in `src/renderer/composables/useGoldPrice.js`
2. Modify the API response handling as needed
3. Consider adding API key configuration in settings

### Configuration

Settings are stored in the user data directory:
- Windows: `%APPDATA%/gold-price-widget/config.json`
- macOS: `~/Library/Application Support/gold-price-widget/config.json`
- Linux: `~/.config/gold-price-widget/config.json`

## Development

### Project Structure

```
gold-price-widget/
├── src/
│   ├── main/           # Electron main process
│   │   ├── main.js     # Main application logic
│   │   └── preload.js  # Preload script for IPC
│   └── renderer/       # Vue 3 application
│       ├── index.html  # Main widget HTML
│       ├── settings.html # Settings window HTML
│       ├── main.js     # Vue app entry point
│       ├── App.vue     # Main widget component
│       ├── settings.js # Settings page logic
│       └── composables/
│           └── useGoldPrice.js # Gold price management
├── assets/             # Application icons and resources
├── dist/              # Built application (generated)
└── package.json       # Project configuration
```

### Available Scripts

- `npm run dev`: Start Vite development server
- `npm run build`: Build Vue application for production
- `npm run electron`: Run Electron with built application
- `npm run electron-dev`: Run in development mode with hot reload
- `npm run dist`: Build distributables for all platforms
- `npm run dist:win`: Build Windows executable
- `npm run dist:mac`: Build macOS application
- `npm run dist:linux`: Build Linux AppImage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Notes

- For production use, replace the mock API with a real gold price API service
- Create proper application icons for better visual appearance
- Consider adding more configuration options (currency, refresh intervals, etc.)
- Add error handling for network connectivity issues
