<template>
  <div class="widget-container">
    <!-- Loading state -->
    <div v-if="isLoading" class="loading">
      ...
    </div>
    
    <!-- Error state -->
    <div v-else-if="hasError" class="error" :title="errorMessage">
      ❗
    </div>
    
    <!-- Price display -->
    <div v-else class="price-display">
      <div class="price-change" v-if="priceChange">
        <span v-if="priceChange === 'up'" class="price-up">↑</span>
        <span v-else-if="priceChange === 'down'" class="price-down">↓</span>
      </div>
      
      <div class="price-value" :class="priceChangeClass">
        ${{ formattedPrice }}
      </div>
      
      <div class="price-unit">
        USD/oz
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useGoldPrice } from './composables/useGoldPrice'

export default {
  name: 'App',
  setup() {
    const {
      price,
      priceChange,
      isLoading,
      hasError,
      errorMessage,
      fetchPrice,
      startAutoUpdate,
      stopAutoUpdate
    } = useGoldPrice()

    const formattedPrice = computed(() => {
      return price.value ? price.value.toFixed(2) : '0.00'
    })

    const priceChangeClass = computed(() => {
      if (priceChange.value === 'up') return 'price-up'
      if (priceChange.value === 'down') return 'price-down'
      return ''
    })

    onMounted(async () => {
      // Initial fetch
      await fetchPrice()
      
      // Start auto-update
      startAutoUpdate()
    })

    onUnmounted(() => {
      stopAutoUpdate()
    })

    return {
      price,
      priceChange,
      isLoading,
      hasError,
      errorMessage,
      formattedPrice,
      priceChangeClass
    }
  }
}
</script>
