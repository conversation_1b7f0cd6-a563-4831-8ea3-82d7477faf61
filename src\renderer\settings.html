<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gold Price Widget - Settings</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      padding: 20px;
    }
    
    .settings-container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 360px;
      margin: 0 auto;
    }
    
    h1 {
      color: #333;
      margin-bottom: 20px;
      font-size: 18px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: 500;
    }
    
    input[type="number"] {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    input[type="number"]:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
    
    .help-text {
      font-size: 12px;
      color: #777;
      margin-top: 5px;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    button {
      flex: 1;
      padding: 10px 16px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background: #667eea;
      color: white;
    }
    
    .btn-primary:hover {
      background: #5a6fd8;
    }
    
    .btn-secondary {
      background: #e9ecef;
      color: #495057;
    }
    
    .btn-secondary:hover {
      background: #dee2e6;
    }
    
    .status-message {
      margin-top: 10px;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
    }
    
    .status-success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status-error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <div class="settings-container">
    <h1>Settings</h1>
    
    <form id="settingsForm">
      <div class="form-group">
        <label for="updateInterval">Update Interval (seconds)</label>
        <input type="number" id="updateInterval" min="5" max="300" value="30">
        <div class="help-text">How often to check for price updates (5-300 seconds)</div>
      </div>
      
      <div class="button-group">
        <button type="button" class="btn-secondary" id="cancelBtn">Cancel</button>
        <button type="submit" class="btn-primary" id="saveBtn">Save</button>
      </div>
    </form>
    
    <div id="statusMessage" class="status-message" style="display: none;"></div>
  </div>

  <script type="module" src="/settings.js"></script>
</body>
</html>
