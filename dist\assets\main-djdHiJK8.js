import"./modulepreload-polyfill-B5Qt9EMX.js";/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function as(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const z={},ut=[],Pe=()=>{},no=()=>!1,dn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),us=e=>e.startsWith("onUpdate:"),ce=Object.assign,ds=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},so=Object.prototype.hasOwnProperty,q=(e,t)=>so.call(e,t),D=Array.isArray,dt=e=>hn(e)==="[object Map]",Or=e=>hn(e)==="[object Set]",j=e=>typeof e=="function",Y=e=>typeof e=="string",ze=e=>typeof e=="symbol",G=e=>e!==null&&typeof e=="object",Ar=e=>(G(e)||j(e))&&j(e.then)&&j(e.catch),Cr=Object.prototype.toString,hn=e=>Cr.call(e),ro=e=>hn(e).slice(8,-1),vr=e=>hn(e)==="[object Object]",hs=e=>Y(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ot=as(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),pn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},io=/-(\w)/g,Ke=pn(e=>e.replace(io,(t,n)=>n?n.toUpperCase():"")),oo=/\B([A-Z])/g,ct=pn(e=>e.replace(oo,"-$1").toLowerCase()),Pr=pn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Pn=pn(e=>e?`on${Pr(e)}`:""),Ve=(e,t)=>!Object.is(e,t),Fn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Kn=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},lo=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Bs;const gn=()=>Bs||(Bs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ps(e){if(D(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=Y(s)?uo(s):ps(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(Y(e)||G(e))return e}const co=/;(?![^(]*\))/g,fo=/:([^]+)/,ao=/\/\*[^]*?\*\//g;function uo(e){const t={};return e.replace(ao,"").split(co).forEach(n=>{if(n){const s=n.split(fo);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function mn(e){let t="";if(Y(e))t=e;else if(D(e))for(let n=0;n<e.length;n++){const s=mn(e[n]);s&&(t+=s+" ")}else if(G(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ho="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",po=as(ho);function Fr(e){return!!e||e===""}const Nr=e=>!!(e&&e.__v_isRef===!0),Ir=e=>Y(e)?e:e==null?"":D(e)||G(e)&&(e.toString===Cr||!j(e.toString))?Nr(e)?Ir(e.value):JSON.stringify(e,Dr,2):String(e),Dr=(e,t)=>Nr(t)?Dr(e,t.value):dt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Nn(s,i)+" =>"]=r,n),{})}:Or(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Nn(n))}:ze(t)?Nn(t):G(t)&&!D(t)&&!vr(t)?String(t):t,Nn=(e,t="")=>{var n;return ze(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ue;class go{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ue,!t&&ue&&(this.index=(ue.scopes||(ue.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ue;try{return ue=this,t()}finally{ue=n}}}on(){++this._on===1&&(this.prevScope=ue,ue=this)}off(){this._on>0&&--this._on===0&&(ue=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function mo(){return ue}let W;const In=new WeakSet;class Mr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ue&&ue.active&&ue.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,In.has(this)&&(In.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ur(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Hs(this),jr(this);const t=W,n=ye;W=this,ye=!0;try{return this.fn()}finally{Br(this),W=t,ye=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)bs(t);this.deps=this.depsTail=void 0,Hs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?In.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Wn(this)&&this.run()}get dirty(){return Wn(this)}}let Lr=0,At,Ct;function Ur(e,t=!1){if(e.flags|=8,t){e.next=Ct,Ct=e;return}e.next=At,At=e}function gs(){Lr++}function ms(){if(--Lr>0)return;if(Ct){let t=Ct;for(Ct=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;At;){let t=At;for(At=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function jr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Br(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),bs(s),bo(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Wn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Hr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Hr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Dt)||(e.globalVersion=Dt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Wn(e))))return;e.flags|=2;const t=e.dep,n=W,s=ye;W=e,ye=!0;try{jr(e);const r=e.fn(e._value);(t.version===0||Ve(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{W=n,ye=s,Br(e),e.flags&=-3}}function bs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)bs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function bo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ye=!0;const $r=[];function Ue(){$r.push(ye),ye=!1}function je(){const e=$r.pop();ye=e===void 0?!0:e}function Hs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=W;W=void 0;try{t()}finally{W=n}}}let Dt=0;class yo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ys{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!W||!ye||W===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==W)n=this.activeLink=new yo(W,this),W.deps?(n.prevDep=W.depsTail,W.depsTail.nextDep=n,W.depsTail=n):W.deps=W.depsTail=n,qr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=W.depsTail,n.nextDep=void 0,W.depsTail.nextDep=n,W.depsTail=n,W.deps===n&&(W.deps=s)}return n}trigger(t){this.version++,Dt++,this.notify(t)}notify(t){gs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ms()}}}function qr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)qr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const zn=new WeakMap,rt=Symbol(""),Jn=Symbol(""),Mt=Symbol("");function ee(e,t,n){if(ye&&W){let s=zn.get(e);s||zn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new ys),r.map=s,r.key=n),r.track()}}function Me(e,t,n,s,r,i){const o=zn.get(e);if(!o){Dt++;return}const l=c=>{c&&c.trigger()};if(gs(),t==="clear")o.forEach(l);else{const c=D(e),u=c&&hs(n);if(c&&n==="length"){const f=Number(s);o.forEach((h,y)=>{(y==="length"||y===Mt||!ze(y)&&y>=f)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),u&&l(o.get(Mt)),t){case"add":c?u&&l(o.get("length")):(l(o.get(rt)),dt(e)&&l(o.get(Jn)));break;case"delete":c||(l(o.get(rt)),dt(e)&&l(o.get(Jn)));break;case"set":dt(e)&&l(o.get(rt));break}}ms()}function ft(e){const t=$(e);return t===e?t:(ee(t,"iterate",Mt),_e(e)?t:t.map(ie))}function _s(e){return ee(e=$(e),"iterate",Mt),e}const _o={__proto__:null,[Symbol.iterator](){return Dn(this,Symbol.iterator,ie)},concat(...e){return ft(this).concat(...e.map(t=>D(t)?ft(t):t))},entries(){return Dn(this,"entries",e=>(e[1]=ie(e[1]),e))},every(e,t){return Ne(this,"every",e,t,void 0,arguments)},filter(e,t){return Ne(this,"filter",e,t,n=>n.map(ie),arguments)},find(e,t){return Ne(this,"find",e,t,ie,arguments)},findIndex(e,t){return Ne(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ne(this,"findLast",e,t,ie,arguments)},findLastIndex(e,t){return Ne(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ne(this,"forEach",e,t,void 0,arguments)},includes(...e){return Mn(this,"includes",e)},indexOf(...e){return Mn(this,"indexOf",e)},join(e){return ft(this).join(e)},lastIndexOf(...e){return Mn(this,"lastIndexOf",e)},map(e,t){return Ne(this,"map",e,t,void 0,arguments)},pop(){return Et(this,"pop")},push(...e){return Et(this,"push",e)},reduce(e,...t){return $s(this,"reduce",e,t)},reduceRight(e,...t){return $s(this,"reduceRight",e,t)},shift(){return Et(this,"shift")},some(e,t){return Ne(this,"some",e,t,void 0,arguments)},splice(...e){return Et(this,"splice",e)},toReversed(){return ft(this).toReversed()},toSorted(e){return ft(this).toSorted(e)},toSpliced(...e){return ft(this).toSpliced(...e)},unshift(...e){return Et(this,"unshift",e)},values(){return Dn(this,"values",ie)}};function Dn(e,t,n){const s=_s(e),r=s[t]();return s!==e&&!_e(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const wo=Array.prototype;function Ne(e,t,n,s,r,i){const o=_s(e),l=o!==e&&!_e(e),c=o[t];if(c!==wo[t]){const h=c.apply(e,i);return l?ie(h):h}let u=n;o!==e&&(l?u=function(h,y){return n.call(this,ie(h),y,e)}:n.length>2&&(u=function(h,y){return n.call(this,h,y,e)}));const f=c.call(o,u,s);return l&&r?r(f):f}function $s(e,t,n,s){const r=_s(e);let i=n;return r!==e&&(_e(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,ie(l),c,e)}),r[t](i,...s)}function Mn(e,t,n){const s=$(e);ee(s,"iterate",Mt);const r=s[t](...n);return(r===-1||r===!1)&&Ss(n[0])?(n[0]=$(n[0]),s[t](...n)):r}function Et(e,t,n=[]){Ue(),gs();const s=$(e)[t].apply(e,n);return ms(),je(),s}const xo=as("__proto__,__v_isRef,__isVue"),kr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ze));function Eo(e){ze(e)||(e=String(e));const t=$(this);return ee(t,"has",e),t.hasOwnProperty(e)}class Vr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?No:Jr:i?zr:Wr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=D(t);if(!r){let c;if(o&&(c=_o[n]))return c;if(n==="hasOwnProperty")return Eo}const l=Reflect.get(t,n,ne(t)?t:s);return(ze(n)?kr.has(n):xo(n))||(r||ee(t,"get",n),i)?l:ne(l)?o&&hs(n)?l:l.value:G(l)?r?Gr(l):xs(l):l}}class Kr extends Vr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=ot(i);if(!_e(s)&&!ot(s)&&(i=$(i),s=$(s)),!D(t)&&ne(i)&&!ne(s))return c?!1:(i.value=s,!0)}const o=D(t)&&hs(n)?Number(n)<t.length:q(t,n),l=Reflect.set(t,n,s,ne(t)?t:r);return t===$(r)&&(o?Ve(s,i)&&Me(t,"set",n,s):Me(t,"add",n,s)),l}deleteProperty(t,n){const s=q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Me(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ze(n)||!kr.has(n))&&ee(t,"has",n),s}ownKeys(t){return ee(t,"iterate",D(t)?"length":rt),Reflect.ownKeys(t)}}class So extends Vr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ro=new Kr,To=new So,Oo=new Kr(!0);const Gn=e=>e,Jt=e=>Reflect.getPrototypeOf(e);function Ao(e,t,n){return function(...s){const r=this.__v_raw,i=$(r),o=dt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,u=r[e](...s),f=n?Gn:t?Xn:ie;return!t&&ee(i,"iterate",c?Jn:rt),{next(){const{value:h,done:y}=u.next();return y?{value:h,done:y}:{value:l?[f(h[0]),f(h[1])]:f(h),done:y}},[Symbol.iterator](){return this}}}}function Gt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Co(e,t){const n={get(r){const i=this.__v_raw,o=$(i),l=$(r);e||(Ve(r,l)&&ee(o,"get",r),ee(o,"get",l));const{has:c}=Jt(o),u=t?Gn:e?Xn:ie;if(c.call(o,r))return u(i.get(r));if(c.call(o,l))return u(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&ee($(r),"iterate",rt),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=$(i),l=$(r);return e||(Ve(r,l)&&ee(o,"has",r),ee(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=$(l),u=t?Gn:e?Xn:ie;return!e&&ee(c,"iterate",rt),l.forEach((f,h)=>r.call(i,u(f),u(h),o))}};return ce(n,e?{add:Gt("add"),set:Gt("set"),delete:Gt("delete"),clear:Gt("clear")}:{add(r){!t&&!_e(r)&&!ot(r)&&(r=$(r));const i=$(this);return Jt(i).has.call(i,r)||(i.add(r),Me(i,"add",r,r)),this},set(r,i){!t&&!_e(i)&&!ot(i)&&(i=$(i));const o=$(this),{has:l,get:c}=Jt(o);let u=l.call(o,r);u||(r=$(r),u=l.call(o,r));const f=c.call(o,r);return o.set(r,i),u?Ve(i,f)&&Me(o,"set",r,i):Me(o,"add",r,i),this},delete(r){const i=$(this),{has:o,get:l}=Jt(i);let c=o.call(i,r);c||(r=$(r),c=o.call(i,r)),l&&l.call(i,r);const u=i.delete(r);return c&&Me(i,"delete",r,void 0),u},clear(){const r=$(this),i=r.size!==0,o=r.clear();return i&&Me(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Ao(r,e,t)}),n}function ws(e,t){const n=Co(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(q(n,r)&&r in s?n:s,r,i)}const vo={get:ws(!1,!1)},Po={get:ws(!1,!0)},Fo={get:ws(!0,!1)};const Wr=new WeakMap,zr=new WeakMap,Jr=new WeakMap,No=new WeakMap;function Io(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Do(e){return e.__v_skip||!Object.isExtensible(e)?0:Io(ro(e))}function xs(e){return ot(e)?e:Es(e,!1,Ro,vo,Wr)}function Mo(e){return Es(e,!1,Oo,Po,zr)}function Gr(e){return Es(e,!0,To,Fo,Jr)}function Es(e,t,n,s,r){if(!G(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Do(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function vt(e){return ot(e)?vt(e.__v_raw):!!(e&&e.__v_isReactive)}function ot(e){return!!(e&&e.__v_isReadonly)}function _e(e){return!!(e&&e.__v_isShallow)}function Ss(e){return e?!!e.__v_raw:!1}function $(e){const t=e&&e.__v_raw;return t?$(t):e}function Lo(e){return!q(e,"__v_skip")&&Object.isExtensible(e)&&Kn(e,"__v_skip",!0),e}const ie=e=>G(e)?xs(e):e,Xn=e=>G(e)?Gr(e):e;function ne(e){return e?e.__v_isRef===!0:!1}function Ze(e){return Uo(e,!1)}function Uo(e,t){return ne(e)?e:new jo(e,t)}class jo{constructor(t,n){this.dep=new ys,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:$(t),this._value=n?t:ie(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||_e(t)||ot(t);t=s?t:$(t),Ve(t,n)&&(this._rawValue=t,this._value=s?t:ie(t),this.dep.trigger())}}function Bo(e){return ne(e)?e.value:e}const Ho={get:(e,t,n)=>t==="__v_raw"?e:Bo(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ne(r)&&!ne(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Xr(e){return vt(e)?e:new Proxy(e,Ho)}class $o{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ys(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Dt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&W!==this)return Ur(this,!0),!0}get value(){const t=this.dep.track();return Hr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function qo(e,t,n=!1){let s,r;return j(e)?s=e:(s=e.get,r=e.set),new $o(s,r,n)}const Xt={},sn=new WeakMap;let nt;function ko(e,t=!1,n=nt){if(n){let s=sn.get(n);s||sn.set(n,s=[]),s.push(e)}}function Vo(e,t,n=z){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,u=v=>r?v:_e(v)||r===!1||r===0?ke(v,1):ke(v);let f,h,y,R,m=!1,E=!1;if(ne(e)?(h=()=>e.value,m=_e(e)):vt(e)?(h=()=>u(e),m=!0):D(e)?(E=!0,m=e.some(v=>vt(v)||_e(v)),h=()=>e.map(v=>{if(ne(v))return v.value;if(vt(v))return u(v);if(j(v))return c?c(v,2):v()})):j(e)?t?h=c?()=>c(e,2):e:h=()=>{if(y){Ue();try{y()}finally{je()}}const v=nt;nt=f;try{return c?c(e,3,[R]):e(R)}finally{nt=v}}:h=Pe,t&&r){const v=h,B=r===!0?1/0:r;h=()=>ke(v(),B)}const O=mo(),N=()=>{f.stop(),O&&O.active&&ds(O.effects,f)};if(i&&t){const v=t;t=(...B)=>{v(...B),N()}}let M=E?new Array(e.length).fill(Xt):Xt;const U=v=>{if(!(!(f.flags&1)||!f.dirty&&!v))if(t){const B=f.run();if(r||m||(E?B.some((Q,Z)=>Ve(Q,M[Z])):Ve(B,M))){y&&y();const Q=nt;nt=f;try{const Z=[B,M===Xt?void 0:E&&M[0]===Xt?[]:M,R];M=B,c?c(t,3,Z):t(...Z)}finally{nt=Q}}}else f.run()};return l&&l(U),f=new Mr(h),f.scheduler=o?()=>o(U,!1):U,R=v=>ko(v,!1,f),y=f.onStop=()=>{const v=sn.get(f);if(v){if(c)c(v,4);else for(const B of v)B();sn.delete(f)}},t?s?U(!0):M=f.run():o?o(U.bind(null,!0),!0):f.run(),N.pause=f.pause.bind(f),N.resume=f.resume.bind(f),N.stop=N,N}function ke(e,t=1/0,n){if(t<=0||!G(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ne(e))ke(e.value,t,n);else if(D(e))for(let s=0;s<e.length;s++)ke(e[s],t,n);else if(Or(e)||dt(e))e.forEach(s=>{ke(s,t,n)});else if(vr(e)){for(const s in e)ke(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ke(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ht(e,t,n,s){try{return s?e(...s):e()}catch(r){bn(r,t,n)}}function Fe(e,t,n,s){if(j(e)){const r=Ht(e,t,n,s);return r&&Ar(r)&&r.catch(i=>{bn(i,t,n)}),r}if(D(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Fe(e[i],t,n,s));return r}}function bn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||z;if(t){let l=t.parent;const c=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const f=l.ec;if(f){for(let h=0;h<f.length;h++)if(f[h](e,c,u)===!1)return}l=l.parent}if(i){Ue(),Ht(i,null,10,[e,c,u]),je();return}}Ko(e,n,r,s,o)}function Ko(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const oe=[];let Ae=-1;const ht=[];let $e=null,at=0;const Yr=Promise.resolve();let rn=null;function Wo(e){const t=rn||Yr;return e?t.then(this?e.bind(this):e):t}function zo(e){let t=Ae+1,n=oe.length;for(;t<n;){const s=t+n>>>1,r=oe[s],i=Lt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function Rs(e){if(!(e.flags&1)){const t=Lt(e),n=oe[oe.length-1];!n||!(e.flags&2)&&t>=Lt(n)?oe.push(e):oe.splice(zo(t),0,e),e.flags|=1,Zr()}}function Zr(){rn||(rn=Yr.then(ei))}function Jo(e){D(e)?ht.push(...e):$e&&e.id===-1?$e.splice(at+1,0,e):e.flags&1||(ht.push(e),e.flags|=1),Zr()}function qs(e,t,n=Ae+1){for(;n<oe.length;n++){const s=oe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;oe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Qr(e){if(ht.length){const t=[...new Set(ht)].sort((n,s)=>Lt(n)-Lt(s));if(ht.length=0,$e){$e.push(...t);return}for($e=t,at=0;at<$e.length;at++){const n=$e[at];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}$e=null,at=0}}const Lt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ei(e){try{for(Ae=0;Ae<oe.length;Ae++){const t=oe[Ae];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ht(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ae<oe.length;Ae++){const t=oe[Ae];t&&(t.flags&=-2)}Ae=-1,oe.length=0,Qr(),rn=null,(oe.length||ht.length)&&ei()}}let ve=null,ti=null;function on(e){const t=ve;return ve=e,ti=e&&e.type.__scopeId||null,t}function Go(e,t=ve,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Ys(-1);const i=on(t);let o;try{o=e(...r)}finally{on(i),s._d&&Ys(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Qe(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(Ue(),Fe(c,n,8,[e.el,l,e,t]),je())}}const Xo=Symbol("_vte"),Yo=e=>e.__isTeleport;function Ts(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ts(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ni(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Pt(e,t,n,s,r=!1){if(D(e)){e.forEach((m,E)=>Pt(m,t&&(D(t)?t[E]:t),n,s,r));return}if(Ft(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Pt(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Ps(s.component):s.el,o=r?null:i,{i:l,r:c}=e,u=t&&t.r,f=l.refs===z?l.refs={}:l.refs,h=l.setupState,y=$(h),R=h===z?()=>!1:m=>q(y,m);if(u!=null&&u!==c&&(Y(u)?(f[u]=null,R(u)&&(h[u]=null)):ne(u)&&(u.value=null)),j(c))Ht(c,l,12,[o,f]);else{const m=Y(c),E=ne(c);if(m||E){const O=()=>{if(e.f){const N=m?R(c)?h[c]:f[c]:c.value;r?D(N)&&ds(N,i):D(N)?N.includes(i)||N.push(i):m?(f[c]=[i],R(c)&&(h[c]=f[c])):(c.value=[i],e.k&&(f[e.k]=c.value))}else m?(f[c]=o,R(c)&&(h[c]=o)):E&&(c.value=o,e.k&&(f[e.k]=o))};o?(O.id=-1,me(O,n)):O()}}}gn().requestIdleCallback;gn().cancelIdleCallback;const Ft=e=>!!e.type.__asyncLoader,si=e=>e.type.__isKeepAlive;function Zo(e,t){ri(e,"a",t)}function Qo(e,t){ri(e,"da",t)}function ri(e,t,n=le){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(yn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)si(r.parent.vnode)&&el(s,t,n,r),r=r.parent}}function el(e,t,n,s){const r=yn(t,e,s,!0);Os(()=>{ds(s[t],r)},n)}function yn(e,t,n=le,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ue();const l=$t(n),c=Fe(t,n,e,o);return l(),je(),c});return s?r.unshift(i):r.push(i),i}}const Be=e=>(t,n=le)=>{(!jt||e==="sp")&&yn(e,(...s)=>t(...s),n)},tl=Be("bm"),ii=Be("m"),nl=Be("bu"),sl=Be("u"),rl=Be("bum"),Os=Be("um"),il=Be("sp"),ol=Be("rtg"),ll=Be("rtc");function cl(e,t=le){yn("ec",e,t)}const fl=Symbol.for("v-ndc"),Yn=e=>e?Oi(e)?Ps(e):Yn(e.parent):null,Nt=ce(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Yn(e.parent),$root:e=>Yn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>li(e),$forceUpdate:e=>e.f||(e.f=()=>{Rs(e.update)}),$nextTick:e=>e.n||(e.n=Wo.bind(e.proxy)),$watch:e=>Fl.bind(e)}),Ln=(e,t)=>e!==z&&!e.__isScriptSetup&&q(e,t),al={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let u;if(t[0]!=="$"){const R=o[t];if(R!==void 0)switch(R){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ln(s,t))return o[t]=1,s[t];if(r!==z&&q(r,t))return o[t]=2,r[t];if((u=e.propsOptions[0])&&q(u,t))return o[t]=3,i[t];if(n!==z&&q(n,t))return o[t]=4,n[t];Zn&&(o[t]=0)}}const f=Nt[t];let h,y;if(f)return t==="$attrs"&&ee(e.attrs,"get",""),f(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==z&&q(n,t))return o[t]=4,n[t];if(y=c.config.globalProperties,q(y,t))return y[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Ln(r,t)?(r[t]=n,!0):s!==z&&q(s,t)?(s[t]=n,!0):q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==z&&q(e,o)||Ln(t,o)||(l=i[0])&&q(l,o)||q(s,o)||q(Nt,o)||q(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ks(e){return D(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Zn=!0;function ul(e){const t=li(e),n=e.proxy,s=e.ctx;Zn=!1,t.beforeCreate&&Vs(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:u,created:f,beforeMount:h,mounted:y,beforeUpdate:R,updated:m,activated:E,deactivated:O,beforeDestroy:N,beforeUnmount:M,destroyed:U,unmounted:v,render:B,renderTracked:Q,renderTriggered:Z,errorCaptured:pe,serverPrefetch:Je,expose:Ge,inheritAttrs:yt,components:Vt,directives:Kt,filters:Cn}=t;if(u&&dl(u,s,null),o)for(const J in o){const V=o[J];j(V)&&(s[J]=V.bind(n))}if(r){const J=r.call(n,n);G(J)&&(e.data=xs(J))}if(Zn=!0,i)for(const J in i){const V=i[J],Xe=j(V)?V.bind(n,n):j(V.get)?V.get.bind(n,n):Pe,Wt=!j(V)&&j(V.set)?V.set.bind(n):Pe,Ye=ns({get:Xe,set:Wt});Object.defineProperty(s,J,{enumerable:!0,configurable:!0,get:()=>Ye.value,set:xe=>Ye.value=xe})}if(l)for(const J in l)oi(l[J],s,n,J);if(c){const J=j(c)?c.call(n):c;Reflect.ownKeys(J).forEach(V=>{yl(V,J[V])})}f&&Vs(f,e,"c");function se(J,V){D(V)?V.forEach(Xe=>J(Xe.bind(n))):V&&J(V.bind(n))}if(se(tl,h),se(ii,y),se(nl,R),se(sl,m),se(Zo,E),se(Qo,O),se(cl,pe),se(ll,Q),se(ol,Z),se(rl,M),se(Os,v),se(il,Je),D(Ge))if(Ge.length){const J=e.exposed||(e.exposed={});Ge.forEach(V=>{Object.defineProperty(J,V,{get:()=>n[V],set:Xe=>n[V]=Xe})})}else e.exposed||(e.exposed={});B&&e.render===Pe&&(e.render=B),yt!=null&&(e.inheritAttrs=yt),Vt&&(e.components=Vt),Kt&&(e.directives=Kt),Je&&ni(e)}function dl(e,t,n=Pe){D(e)&&(e=Qn(e));for(const s in e){const r=e[s];let i;G(r)?"default"in r?i=Yt(r.from||s,r.default,!0):i=Yt(r.from||s):i=Yt(r),ne(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Vs(e,t,n){Fe(D(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function oi(e,t,n,s){let r=s.includes(".")?wi(n,s):()=>n[s];if(Y(e)){const i=t[e];j(i)&&jn(r,i)}else if(j(e))jn(r,e.bind(n));else if(G(e))if(D(e))e.forEach(i=>oi(i,t,n,s));else{const i=j(e.handler)?e.handler.bind(n):t[e.handler];j(i)&&jn(r,i,e)}}function li(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(u=>ln(c,u,o,!0)),ln(c,t,o)),G(t)&&i.set(t,c),c}function ln(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&ln(e,i,n,!0),r&&r.forEach(o=>ln(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=hl[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const hl={data:Ks,props:Ws,emits:Ws,methods:Tt,computed:Tt,beforeCreate:re,created:re,beforeMount:re,mounted:re,beforeUpdate:re,updated:re,beforeDestroy:re,beforeUnmount:re,destroyed:re,unmounted:re,activated:re,deactivated:re,errorCaptured:re,serverPrefetch:re,components:Tt,directives:Tt,watch:gl,provide:Ks,inject:pl};function Ks(e,t){return t?e?function(){return ce(j(e)?e.call(this,this):e,j(t)?t.call(this,this):t)}:t:e}function pl(e,t){return Tt(Qn(e),Qn(t))}function Qn(e){if(D(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function re(e,t){return e?[...new Set([].concat(e,t))]:t}function Tt(e,t){return e?ce(Object.create(null),e,t):t}function Ws(e,t){return e?D(e)&&D(t)?[...new Set([...e,...t])]:ce(Object.create(null),ks(e),ks(t??{})):t}function gl(e,t){if(!e)return t;if(!t)return e;const n=ce(Object.create(null),e);for(const s in t)n[s]=re(e[s],t[s]);return n}function ci(){return{app:null,config:{isNativeTag:no,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ml=0;function bl(e,t){return function(s,r=null){j(s)||(s=ce({},s)),r!=null&&!G(r)&&(r=null);const i=ci(),o=new WeakSet,l=[];let c=!1;const u=i.app={_uid:ml++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Ql,get config(){return i.config},set config(f){},use(f,...h){return o.has(f)||(f&&j(f.install)?(o.add(f),f.install(u,...h)):j(f)&&(o.add(f),f(u,...h))),u},mixin(f){return i.mixins.includes(f)||i.mixins.push(f),u},component(f,h){return h?(i.components[f]=h,u):i.components[f]},directive(f,h){return h?(i.directives[f]=h,u):i.directives[f]},mount(f,h,y){if(!c){const R=u._ceVNode||Le(s,r);return R.appContext=i,y===!0?y="svg":y===!1&&(y=void 0),e(R,f,y),c=!0,u._container=f,f.__vue_app__=u,Ps(R.component)}},onUnmount(f){l.push(f)},unmount(){c&&(Fe(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(f,h){return i.provides[f]=h,u},runWithContext(f){const h=pt;pt=u;try{return f()}finally{pt=h}}};return u}}let pt=null;function yl(e,t){if(le){let n=le.provides;const s=le.parent&&le.parent.provides;s===n&&(n=le.provides=Object.create(s)),n[e]=t}}function Yt(e,t,n=!1){const s=le||ve;if(s||pt){let r=pt?pt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&j(t)?t.call(s&&s.proxy):t}}const fi={},ai=()=>Object.create(fi),ui=e=>Object.getPrototypeOf(e)===fi;function _l(e,t,n,s=!1){const r={},i=ai();e.propsDefaults=Object.create(null),di(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Mo(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function wl(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=$(r),[c]=e.propsOptions;let u=!1;if((s||o>0)&&!(o&16)){if(o&8){const f=e.vnode.dynamicProps;for(let h=0;h<f.length;h++){let y=f[h];if(_n(e.emitsOptions,y))continue;const R=t[y];if(c)if(q(i,y))R!==i[y]&&(i[y]=R,u=!0);else{const m=Ke(y);r[m]=es(c,l,m,R,e,!1)}else R!==i[y]&&(i[y]=R,u=!0)}}}else{di(e,t,r,i)&&(u=!0);let f;for(const h in l)(!t||!q(t,h)&&((f=ct(h))===h||!q(t,f)))&&(c?n&&(n[h]!==void 0||n[f]!==void 0)&&(r[h]=es(c,l,h,void 0,e,!0)):delete r[h]);if(i!==l)for(const h in i)(!t||!q(t,h))&&(delete i[h],u=!0)}u&&Me(e.attrs,"set","")}function di(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Ot(c))continue;const u=t[c];let f;r&&q(r,f=Ke(c))?!i||!i.includes(f)?n[f]=u:(l||(l={}))[f]=u:_n(e.emitsOptions,c)||(!(c in s)||u!==s[c])&&(s[c]=u,o=!0)}if(i){const c=$(n),u=l||z;for(let f=0;f<i.length;f++){const h=i[f];n[h]=es(r,c,h,u[h],e,!q(u,h))}}return o}function es(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=q(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&j(c)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const f=$t(r);s=u[n]=c.call(null,t),f()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===ct(n))&&(s=!0))}return s}const xl=new WeakMap;function hi(e,t,n=!1){const s=n?xl:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!j(e)){const f=h=>{c=!0;const[y,R]=hi(h,t,!0);ce(o,y),R&&l.push(...R)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!i&&!c)return G(e)&&s.set(e,ut),ut;if(D(i))for(let f=0;f<i.length;f++){const h=Ke(i[f]);zs(h)&&(o[h]=z)}else if(i)for(const f in i){const h=Ke(f);if(zs(h)){const y=i[f],R=o[h]=D(y)||j(y)?{type:y}:ce({},y),m=R.type;let E=!1,O=!0;if(D(m))for(let N=0;N<m.length;++N){const M=m[N],U=j(M)&&M.name;if(U==="Boolean"){E=!0;break}else U==="String"&&(O=!1)}else E=j(m)&&m.name==="Boolean";R[0]=E,R[1]=O,(E||q(R,"default"))&&l.push(h)}}const u=[o,l];return G(e)&&s.set(e,u),u}function zs(e){return e[0]!=="$"&&!Ot(e)}const As=e=>e[0]==="_"||e==="$stable",Cs=e=>D(e)?e.map(Ce):[Ce(e)],El=(e,t,n)=>{if(t._n)return t;const s=Go((...r)=>Cs(t(...r)),n);return s._c=!1,s},pi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(As(r))continue;const i=e[r];if(j(i))t[r]=El(r,i,s);else if(i!=null){const o=Cs(i);t[r]=()=>o}}},gi=(e,t)=>{const n=Cs(t);e.slots.default=()=>n},mi=(e,t,n)=>{for(const s in t)(n||!As(s))&&(e[s]=t[s])},Sl=(e,t,n)=>{const s=e.slots=ai();if(e.vnode.shapeFlag&32){const r=t.__;r&&Kn(s,"__",r,!0);const i=t._;i?(mi(s,t,n),n&&Kn(s,"_",i,!0)):pi(t,s)}else t&&gi(e,t)},Rl=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=z;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:mi(r,t,n):(i=!t.$stable,pi(t,r)),o=t}else t&&(gi(e,t),o={default:1});if(i)for(const l in r)!As(l)&&o[l]==null&&delete r[l]},me=jl;function Tl(e){return Ol(e)}function Ol(e,t){const n=gn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:u,setElementText:f,parentNode:h,nextSibling:y,setScopeId:R=Pe,insertStaticContent:m}=e,E=(a,d,g,w=null,b=null,_=null,A=void 0,T=null,S=!!d.dynamicChildren)=>{if(a===d)return;a&&!St(a,d)&&(w=zt(a),xe(a,b,_,!0),a=null),d.patchFlag===-2&&(S=!1,d.dynamicChildren=null);const{type:x,ref:F,shapeFlag:C}=d;switch(x){case wn:O(a,d,g,w);break;case We:N(a,d,g,w);break;case Bn:a==null&&M(d,g,w,A);break;case De:Vt(a,d,g,w,b,_,A,T,S);break;default:C&1?B(a,d,g,w,b,_,A,T,S):C&6?Kt(a,d,g,w,b,_,A,T,S):(C&64||C&128)&&x.process(a,d,g,w,b,_,A,T,S,wt)}F!=null&&b?Pt(F,a&&a.ref,_,d||a,!d):F==null&&a&&a.ref!=null&&Pt(a.ref,null,_,a,!0)},O=(a,d,g,w)=>{if(a==null)s(d.el=l(d.children),g,w);else{const b=d.el=a.el;d.children!==a.children&&u(b,d.children)}},N=(a,d,g,w)=>{a==null?s(d.el=c(d.children||""),g,w):d.el=a.el},M=(a,d,g,w)=>{[a.el,a.anchor]=m(a.children,d,g,w,a.el,a.anchor)},U=({el:a,anchor:d},g,w)=>{let b;for(;a&&a!==d;)b=y(a),s(a,g,w),a=b;s(d,g,w)},v=({el:a,anchor:d})=>{let g;for(;a&&a!==d;)g=y(a),r(a),a=g;r(d)},B=(a,d,g,w,b,_,A,T,S)=>{d.type==="svg"?A="svg":d.type==="math"&&(A="mathml"),a==null?Q(d,g,w,b,_,A,T,S):Je(a,d,b,_,A,T,S)},Q=(a,d,g,w,b,_,A,T)=>{let S,x;const{props:F,shapeFlag:C,transition:P,dirs:I}=a;if(S=a.el=o(a.type,_,F&&F.is,F),C&8?f(S,a.children):C&16&&pe(a.children,S,null,w,b,Un(a,_),A,T),I&&Qe(a,null,w,"created"),Z(S,a,a.scopeId,A,w),F){for(const K in F)K!=="value"&&!Ot(K)&&i(S,K,null,F[K],_,w);"value"in F&&i(S,"value",null,F.value,_),(x=F.onVnodeBeforeMount)&&Te(x,w,a)}I&&Qe(a,null,w,"beforeMount");const H=Al(b,P);H&&P.beforeEnter(S),s(S,d,g),((x=F&&F.onVnodeMounted)||H||I)&&me(()=>{x&&Te(x,w,a),H&&P.enter(S),I&&Qe(a,null,w,"mounted")},b)},Z=(a,d,g,w,b)=>{if(g&&R(a,g),w)for(let _=0;_<w.length;_++)R(a,w[_]);if(b){let _=b.subTree;if(d===_||Ei(_.type)&&(_.ssContent===d||_.ssFallback===d)){const A=b.vnode;Z(a,A,A.scopeId,A.slotScopeIds,b.parent)}}},pe=(a,d,g,w,b,_,A,T,S=0)=>{for(let x=S;x<a.length;x++){const F=a[x]=T?qe(a[x]):Ce(a[x]);E(null,F,d,g,w,b,_,A,T)}},Je=(a,d,g,w,b,_,A)=>{const T=d.el=a.el;let{patchFlag:S,dynamicChildren:x,dirs:F}=d;S|=a.patchFlag&16;const C=a.props||z,P=d.props||z;let I;if(g&&et(g,!1),(I=P.onVnodeBeforeUpdate)&&Te(I,g,d,a),F&&Qe(d,a,g,"beforeUpdate"),g&&et(g,!0),(C.innerHTML&&P.innerHTML==null||C.textContent&&P.textContent==null)&&f(T,""),x?Ge(a.dynamicChildren,x,T,g,w,Un(d,b),_):A||V(a,d,T,null,g,w,Un(d,b),_,!1),S>0){if(S&16)yt(T,C,P,g,b);else if(S&2&&C.class!==P.class&&i(T,"class",null,P.class,b),S&4&&i(T,"style",C.style,P.style,b),S&8){const H=d.dynamicProps;for(let K=0;K<H.length;K++){const k=H[K],fe=C[k],ae=P[k];(ae!==fe||k==="value")&&i(T,k,fe,ae,b,g)}}S&1&&a.children!==d.children&&f(T,d.children)}else!A&&x==null&&yt(T,C,P,g,b);((I=P.onVnodeUpdated)||F)&&me(()=>{I&&Te(I,g,d,a),F&&Qe(d,a,g,"updated")},w)},Ge=(a,d,g,w,b,_,A)=>{for(let T=0;T<d.length;T++){const S=a[T],x=d[T],F=S.el&&(S.type===De||!St(S,x)||S.shapeFlag&198)?h(S.el):g;E(S,x,F,null,w,b,_,A,!0)}},yt=(a,d,g,w,b)=>{if(d!==g){if(d!==z)for(const _ in d)!Ot(_)&&!(_ in g)&&i(a,_,d[_],null,b,w);for(const _ in g){if(Ot(_))continue;const A=g[_],T=d[_];A!==T&&_!=="value"&&i(a,_,T,A,b,w)}"value"in g&&i(a,"value",d.value,g.value,b)}},Vt=(a,d,g,w,b,_,A,T,S)=>{const x=d.el=a?a.el:l(""),F=d.anchor=a?a.anchor:l("");let{patchFlag:C,dynamicChildren:P,slotScopeIds:I}=d;I&&(T=T?T.concat(I):I),a==null?(s(x,g,w),s(F,g,w),pe(d.children||[],g,F,b,_,A,T,S)):C>0&&C&64&&P&&a.dynamicChildren?(Ge(a.dynamicChildren,P,g,b,_,A,T),(d.key!=null||b&&d===b.subTree)&&bi(a,d,!0)):V(a,d,g,F,b,_,A,T,S)},Kt=(a,d,g,w,b,_,A,T,S)=>{d.slotScopeIds=T,a==null?d.shapeFlag&512?b.ctx.activate(d,g,w,A,S):Cn(d,g,w,b,_,A,S):Ds(a,d,S)},Cn=(a,d,g,w,b,_,A)=>{const T=a.component=zl(a,w,b);if(si(a)&&(T.ctx.renderer=wt),Jl(T,!1,A),T.asyncDep){if(b&&b.registerDep(T,se,A),!a.el){const S=T.subTree=Le(We);N(null,S,d,g)}}else se(T,a,d,g,b,_,A)},Ds=(a,d,g)=>{const w=d.component=a.component;if(Ll(a,d,g))if(w.asyncDep&&!w.asyncResolved){J(w,d,g);return}else w.next=d,w.update();else d.el=a.el,w.vnode=d},se=(a,d,g,w,b,_,A)=>{const T=()=>{if(a.isMounted){let{next:C,bu:P,u:I,parent:H,vnode:K}=a;{const Se=yi(a);if(Se){C&&(C.el=K.el,J(a,C,A)),Se.asyncDep.then(()=>{a.isUnmounted||T()});return}}let k=C,fe;et(a,!1),C?(C.el=K.el,J(a,C,A)):C=K,P&&Fn(P),(fe=C.props&&C.props.onVnodeBeforeUpdate)&&Te(fe,H,C,K),et(a,!0);const ae=Gs(a),Ee=a.subTree;a.subTree=ae,E(Ee,ae,h(Ee.el),zt(Ee),a,b,_),C.el=ae.el,k===null&&Ul(a,ae.el),I&&me(I,b),(fe=C.props&&C.props.onVnodeUpdated)&&me(()=>Te(fe,H,C,K),b)}else{let C;const{el:P,props:I}=d,{bm:H,m:K,parent:k,root:fe,type:ae}=a,Ee=Ft(d);et(a,!1),H&&Fn(H),!Ee&&(C=I&&I.onVnodeBeforeMount)&&Te(C,k,d),et(a,!0);{fe.ce&&fe.ce._def.shadowRoot!==!1&&fe.ce._injectChildStyle(ae);const Se=a.subTree=Gs(a);E(null,Se,g,w,a,b,_),d.el=Se.el}if(K&&me(K,b),!Ee&&(C=I&&I.onVnodeMounted)){const Se=d;me(()=>Te(C,k,Se),b)}(d.shapeFlag&256||k&&Ft(k.vnode)&&k.vnode.shapeFlag&256)&&a.a&&me(a.a,b),a.isMounted=!0,d=g=w=null}};a.scope.on();const S=a.effect=new Mr(T);a.scope.off();const x=a.update=S.run.bind(S),F=a.job=S.runIfDirty.bind(S);F.i=a,F.id=a.uid,S.scheduler=()=>Rs(F),et(a,!0),x()},J=(a,d,g)=>{d.component=a;const w=a.vnode.props;a.vnode=d,a.next=null,wl(a,d.props,w,g),Rl(a,d.children,g),Ue(),qs(a),je()},V=(a,d,g,w,b,_,A,T,S=!1)=>{const x=a&&a.children,F=a?a.shapeFlag:0,C=d.children,{patchFlag:P,shapeFlag:I}=d;if(P>0){if(P&128){Wt(x,C,g,w,b,_,A,T,S);return}else if(P&256){Xe(x,C,g,w,b,_,A,T,S);return}}I&8?(F&16&&_t(x,b,_),C!==x&&f(g,C)):F&16?I&16?Wt(x,C,g,w,b,_,A,T,S):_t(x,b,_,!0):(F&8&&f(g,""),I&16&&pe(C,g,w,b,_,A,T,S))},Xe=(a,d,g,w,b,_,A,T,S)=>{a=a||ut,d=d||ut;const x=a.length,F=d.length,C=Math.min(x,F);let P;for(P=0;P<C;P++){const I=d[P]=S?qe(d[P]):Ce(d[P]);E(a[P],I,g,null,b,_,A,T,S)}x>F?_t(a,b,_,!0,!1,C):pe(d,g,w,b,_,A,T,S,C)},Wt=(a,d,g,w,b,_,A,T,S)=>{let x=0;const F=d.length;let C=a.length-1,P=F-1;for(;x<=C&&x<=P;){const I=a[x],H=d[x]=S?qe(d[x]):Ce(d[x]);if(St(I,H))E(I,H,g,null,b,_,A,T,S);else break;x++}for(;x<=C&&x<=P;){const I=a[C],H=d[P]=S?qe(d[P]):Ce(d[P]);if(St(I,H))E(I,H,g,null,b,_,A,T,S);else break;C--,P--}if(x>C){if(x<=P){const I=P+1,H=I<F?d[I].el:w;for(;x<=P;)E(null,d[x]=S?qe(d[x]):Ce(d[x]),g,H,b,_,A,T,S),x++}}else if(x>P)for(;x<=C;)xe(a[x],b,_,!0),x++;else{const I=x,H=x,K=new Map;for(x=H;x<=P;x++){const ge=d[x]=S?qe(d[x]):Ce(d[x]);ge.key!=null&&K.set(ge.key,x)}let k,fe=0;const ae=P-H+1;let Ee=!1,Se=0;const xt=new Array(ae);for(x=0;x<ae;x++)xt[x]=0;for(x=I;x<=C;x++){const ge=a[x];if(fe>=ae){xe(ge,b,_,!0);continue}let Re;if(ge.key!=null)Re=K.get(ge.key);else for(k=H;k<=P;k++)if(xt[k-H]===0&&St(ge,d[k])){Re=k;break}Re===void 0?xe(ge,b,_,!0):(xt[Re-H]=x+1,Re>=Se?Se=Re:Ee=!0,E(ge,d[Re],g,null,b,_,A,T,S),fe++)}const Us=Ee?Cl(xt):ut;for(k=Us.length-1,x=ae-1;x>=0;x--){const ge=H+x,Re=d[ge],js=ge+1<F?d[ge+1].el:w;xt[x]===0?E(null,Re,g,js,b,_,A,T,S):Ee&&(k<0||x!==Us[k]?Ye(Re,g,js,2):k--)}}},Ye=(a,d,g,w,b=null)=>{const{el:_,type:A,transition:T,children:S,shapeFlag:x}=a;if(x&6){Ye(a.component.subTree,d,g,w);return}if(x&128){a.suspense.move(d,g,w);return}if(x&64){A.move(a,d,g,wt);return}if(A===De){s(_,d,g);for(let C=0;C<S.length;C++)Ye(S[C],d,g,w);s(a.anchor,d,g);return}if(A===Bn){U(a,d,g);return}if(w!==2&&x&1&&T)if(w===0)T.beforeEnter(_),s(_,d,g),me(()=>T.enter(_),b);else{const{leave:C,delayLeave:P,afterLeave:I}=T,H=()=>{a.ctx.isUnmounted?r(_):s(_,d,g)},K=()=>{C(_,()=>{H(),I&&I()})};P?P(_,H,K):K()}else s(_,d,g)},xe=(a,d,g,w=!1,b=!1)=>{const{type:_,props:A,ref:T,children:S,dynamicChildren:x,shapeFlag:F,patchFlag:C,dirs:P,cacheIndex:I}=a;if(C===-2&&(b=!1),T!=null&&(Ue(),Pt(T,null,g,a,!0),je()),I!=null&&(d.renderCache[I]=void 0),F&256){d.ctx.deactivate(a);return}const H=F&1&&P,K=!Ft(a);let k;if(K&&(k=A&&A.onVnodeBeforeUnmount)&&Te(k,d,a),F&6)to(a.component,g,w);else{if(F&128){a.suspense.unmount(g,w);return}H&&Qe(a,null,d,"beforeUnmount"),F&64?a.type.remove(a,d,g,wt,w):x&&!x.hasOnce&&(_!==De||C>0&&C&64)?_t(x,d,g,!1,!0):(_===De&&C&384||!b&&F&16)&&_t(S,d,g),w&&Ms(a)}(K&&(k=A&&A.onVnodeUnmounted)||H)&&me(()=>{k&&Te(k,d,a),H&&Qe(a,null,d,"unmounted")},g)},Ms=a=>{const{type:d,el:g,anchor:w,transition:b}=a;if(d===De){eo(g,w);return}if(d===Bn){v(a);return}const _=()=>{r(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(a.shapeFlag&1&&b&&!b.persisted){const{leave:A,delayLeave:T}=b,S=()=>A(g,_);T?T(a.el,_,S):S()}else _()},eo=(a,d)=>{let g;for(;a!==d;)g=y(a),r(a),a=g;r(d)},to=(a,d,g)=>{const{bum:w,scope:b,job:_,subTree:A,um:T,m:S,a:x,parent:F,slots:{__:C}}=a;Js(S),Js(x),w&&Fn(w),F&&D(C)&&C.forEach(P=>{F.renderCache[P]=void 0}),b.stop(),_&&(_.flags|=8,xe(A,a,d,g)),T&&me(T,d),me(()=>{a.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&a.asyncDep&&!a.asyncResolved&&a.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},_t=(a,d,g,w=!1,b=!1,_=0)=>{for(let A=_;A<a.length;A++)xe(a[A],d,g,w,b)},zt=a=>{if(a.shapeFlag&6)return zt(a.component.subTree);if(a.shapeFlag&128)return a.suspense.next();const d=y(a.anchor||a.el),g=d&&d[Xo];return g?y(g):d};let vn=!1;const Ls=(a,d,g)=>{a==null?d._vnode&&xe(d._vnode,null,null,!0):E(d._vnode||null,a,d,null,null,null,g),d._vnode=a,vn||(vn=!0,qs(),Qr(),vn=!1)},wt={p:E,um:xe,m:Ye,r:Ms,mt:Cn,mc:pe,pc:V,pbc:Ge,n:zt,o:e};return{render:Ls,hydrate:void 0,createApp:bl(Ls)}}function Un({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function et({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Al(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function bi(e,t,n=!1){const s=e.children,r=t.children;if(D(s)&&D(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=qe(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&bi(o,l)),l.type===wn&&(l.el=o.el),l.type===We&&!l.el&&(l.el=o.el)}}function Cl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<u?i=l+1:o=l;u<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function yi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:yi(t)}function Js(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const vl=Symbol.for("v-scx"),Pl=()=>Yt(vl);function jn(e,t,n){return _i(e,t,n)}function _i(e,t,n=z){const{immediate:s,deep:r,flush:i,once:o}=n,l=ce({},n),c=t&&s||!t&&i!=="post";let u;if(jt){if(i==="sync"){const R=Pl();u=R.__watcherHandles||(R.__watcherHandles=[])}else if(!c){const R=()=>{};return R.stop=Pe,R.resume=Pe,R.pause=Pe,R}}const f=le;l.call=(R,m,E)=>Fe(R,f,m,E);let h=!1;i==="post"?l.scheduler=R=>{me(R,f&&f.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(R,m)=>{m?R():Rs(R)}),l.augmentJob=R=>{t&&(R.flags|=4),h&&(R.flags|=2,f&&(R.id=f.uid,R.i=f))};const y=Vo(e,t,l);return jt&&(u?u.push(y):c&&y()),y}function Fl(e,t,n){const s=this.proxy,r=Y(e)?e.includes(".")?wi(s,e):()=>s[e]:e.bind(s,s);let i;j(t)?i=t:(i=t.handler,n=t);const o=$t(this),l=_i(r,i.bind(s),n);return o(),l}function wi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const Nl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${ct(t)}Modifiers`];function Il(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||z;let r=n;const i=t.startsWith("update:"),o=i&&Nl(s,t.slice(7));o&&(o.trim&&(r=n.map(f=>Y(f)?f.trim():f)),o.number&&(r=n.map(lo)));let l,c=s[l=Pn(t)]||s[l=Pn(Ke(t))];!c&&i&&(c=s[l=Pn(ct(t))]),c&&Fe(c,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Fe(u,e,6,r)}}function xi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!j(e)){const c=u=>{const f=xi(u,t,!0);f&&(l=!0,ce(o,f))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(G(e)&&s.set(e,null),null):(D(i)?i.forEach(c=>o[c]=null):ce(o,i),G(e)&&s.set(e,o),o)}function _n(e,t){return!e||!dn(t)?!1:(t=t.slice(2).replace(/Once$/,""),q(e,t[0].toLowerCase()+t.slice(1))||q(e,ct(t))||q(e,t))}function Gs(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:u,renderCache:f,props:h,data:y,setupState:R,ctx:m,inheritAttrs:E}=e,O=on(e);let N,M;try{if(n.shapeFlag&4){const v=r||s,B=v;N=Ce(u.call(B,v,f,h,R,y,m)),M=l}else{const v=t;N=Ce(v.length>1?v(h,{attrs:l,slots:o,emit:c}):v(h,null)),M=t.props?l:Dl(l)}}catch(v){It.length=0,bn(v,e,1),N=Le(We)}let U=N;if(M&&E!==!1){const v=Object.keys(M),{shapeFlag:B}=U;v.length&&B&7&&(i&&v.some(us)&&(M=Ml(M,i)),U=gt(U,M,!1,!0))}return n.dirs&&(U=gt(U,null,!1,!0),U.dirs=U.dirs?U.dirs.concat(n.dirs):n.dirs),n.transition&&Ts(U,n.transition),N=U,on(O),N}const Dl=e=>{let t;for(const n in e)(n==="class"||n==="style"||dn(n))&&((t||(t={}))[n]=e[n]);return t},Ml=(e,t)=>{const n={};for(const s in e)(!us(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Ll(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Xs(s,o,u):!!o;if(c&8){const f=t.dynamicProps;for(let h=0;h<f.length;h++){const y=f[h];if(o[y]!==s[y]&&!_n(u,y))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?Xs(s,o,u):!0:!!o;return!1}function Xs(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!_n(n,i))return!0}return!1}function Ul({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ei=e=>e.__isSuspense;function jl(e,t){t&&t.pendingBranch?D(e)?t.effects.push(...e):t.effects.push(e):Jo(e)}const De=Symbol.for("v-fgt"),wn=Symbol.for("v-txt"),We=Symbol.for("v-cmt"),Bn=Symbol.for("v-stc"),It=[];let be=null;function He(e=!1){It.push(be=e?null:[])}function Bl(){It.pop(),be=It[It.length-1]||null}let Ut=1;function Ys(e,t=!1){Ut+=e,e<0&&be&&t&&(be.hasOnce=!0)}function Si(e){return e.dynamicChildren=Ut>0?be||ut:null,Bl(),Ut>0&&be&&be.push(e),e}function tt(e,t,n,s,r,i){return Si(cn(e,t,n,s,r,i,!0))}function Hl(e,t,n,s,r){return Si(Le(e,t,n,s,r,!0))}function Ri(e){return e?e.__v_isVNode===!0:!1}function St(e,t){return e.type===t.type&&e.key===t.key}const Ti=({key:e})=>e??null,Zt=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Y(e)||ne(e)||j(e)?{i:ve,r:e,k:t,f:!!n}:e:null);function cn(e,t=null,n=null,s=0,r=null,i=e===De?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ti(t),ref:t&&Zt(t),scopeId:ti,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ve};return l?(vs(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=Y(n)?8:16),Ut>0&&!o&&be&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&be.push(c),c}const Le=$l;function $l(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===fl)&&(e=We),Ri(e)){const l=gt(e,t,!0);return n&&vs(l,n),Ut>0&&!i&&be&&(l.shapeFlag&6?be[be.indexOf(e)]=l:be.push(l)),l.patchFlag=-2,l}if(Zl(e)&&(e=e.__vccOpts),t){t=ql(t);let{class:l,style:c}=t;l&&!Y(l)&&(t.class=mn(l)),G(c)&&(Ss(c)&&!D(c)&&(c=ce({},c)),t.style=ps(c))}const o=Y(e)?1:Ei(e)?128:Yo(e)?64:G(e)?4:j(e)?2:0;return cn(e,t,n,s,r,o,i,!0)}function ql(e){return e?Ss(e)||ui(e)?ce({},e):e:null}function gt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,u=t?Vl(r||{},t):r,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Ti(u),ref:t&&t.ref?n&&i?D(i)?i.concat(Zt(t)):[i,Zt(t)]:Zt(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==De?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&gt(e.ssContent),ssFallback:e.ssFallback&&gt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ts(f,c.clone(f)),f}function kl(e=" ",t=0){return Le(wn,null,e,t)}function Zs(e="",t=!1){return t?(He(),Hl(We,null,e)):Le(We,null,e)}function Ce(e){return e==null||typeof e=="boolean"?Le(We):D(e)?Le(De,null,e.slice()):Ri(e)?qe(e):Le(wn,null,String(e))}function qe(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:gt(e)}function vs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(D(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),vs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!ui(t)?t._ctx=ve:r===3&&ve&&(ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else j(t)?(t={default:t,_ctx:ve},n=32):(t=String(t),s&64?(n=16,t=[kl(t)]):n=8);e.children=t,e.shapeFlag|=n}function Vl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=mn([t.class,s.class]));else if(r==="style")t.style=ps([t.style,s.style]);else if(dn(r)){const i=t[r],o=s[r];o&&i!==o&&!(D(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Te(e,t,n,s=null){Fe(e,t,7,[n,s])}const Kl=ci();let Wl=0;function zl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Kl,i={uid:Wl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new go(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:hi(s,r),emitsOptions:xi(s,r),emit:null,emitted:null,propsDefaults:z,inheritAttrs:s.inheritAttrs,ctx:z,data:z,props:z,attrs:z,slots:z,refs:z,setupState:z,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Il.bind(null,i),e.ce&&e.ce(i),i}let le=null,fn,ts;{const e=gn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};fn=t("__VUE_INSTANCE_SETTERS__",n=>le=n),ts=t("__VUE_SSR_SETTERS__",n=>jt=n)}const $t=e=>{const t=le;return fn(e),e.scope.on(),()=>{e.scope.off(),fn(t)}},Qs=()=>{le&&le.scope.off(),fn(null)};function Oi(e){return e.vnode.shapeFlag&4}let jt=!1;function Jl(e,t=!1,n=!1){t&&ts(t);const{props:s,children:r}=e.vnode,i=Oi(e);_l(e,s,i,t),Sl(e,r,n||t);const o=i?Gl(e,t):void 0;return t&&ts(!1),o}function Gl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,al);const{setup:s}=n;if(s){Ue();const r=e.setupContext=s.length>1?Yl(e):null,i=$t(e),o=Ht(s,e,0,[e.props,r]),l=Ar(o);if(je(),i(),(l||e.sp)&&!Ft(e)&&ni(e),l){if(o.then(Qs,Qs),t)return o.then(c=>{er(e,c)}).catch(c=>{bn(c,e,0)});e.asyncDep=o}else er(e,o)}else Ai(e)}function er(e,t,n){j(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:G(t)&&(e.setupState=Xr(t)),Ai(e)}function Ai(e,t,n){const s=e.type;e.render||(e.render=s.render||Pe);{const r=$t(e);Ue();try{ul(e)}finally{je(),r()}}}const Xl={get(e,t){return ee(e,"get",""),e[t]}};function Yl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Xl),slots:e.slots,emit:e.emit,expose:t}}function Ps(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Xr(Lo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Nt)return Nt[n](e)},has(t,n){return n in t||n in Nt}})):e.proxy}function Zl(e){return j(e)&&"__vccOpts"in e}const ns=(e,t)=>qo(e,t,jt),Ql="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ss;const tr=typeof window<"u"&&window.trustedTypes;if(tr)try{ss=tr.createPolicy("vue",{createHTML:e=>e})}catch{}const Ci=ss?e=>ss.createHTML(e):e=>e,ec="http://www.w3.org/2000/svg",tc="http://www.w3.org/1998/Math/MathML",Ie=typeof document<"u"?document:null,nr=Ie&&Ie.createElement("template"),nc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ie.createElementNS(ec,e):t==="mathml"?Ie.createElementNS(tc,e):n?Ie.createElement(e,{is:n}):Ie.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ie.createTextNode(e),createComment:e=>Ie.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ie.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{nr.innerHTML=Ci(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=nr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},sc=Symbol("_vtc");function rc(e,t,n){const s=e[sc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const sr=Symbol("_vod"),ic=Symbol("_vsh"),oc=Symbol(""),lc=/(^|;)\s*display\s*:/;function cc(e,t,n){const s=e.style,r=Y(n);let i=!1;if(n&&!r){if(t)if(Y(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&Qt(s,l,"")}else for(const o in t)n[o]==null&&Qt(s,o,"");for(const o in n)o==="display"&&(i=!0),Qt(s,o,n[o])}else if(r){if(t!==n){const o=s[oc];o&&(n+=";"+o),s.cssText=n,i=lc.test(n)}}else t&&e.removeAttribute("style");sr in e&&(e[sr]=i?s.display:"",e[ic]&&(s.display="none"))}const rr=/\s*!important$/;function Qt(e,t,n){if(D(n))n.forEach(s=>Qt(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=fc(e,t);rr.test(n)?e.setProperty(ct(s),n.replace(rr,""),"important"):e[s]=n}}const ir=["Webkit","Moz","ms"],Hn={};function fc(e,t){const n=Hn[t];if(n)return n;let s=Ke(t);if(s!=="filter"&&s in e)return Hn[t]=s;s=Pr(s);for(let r=0;r<ir.length;r++){const i=ir[r]+s;if(i in e)return Hn[t]=i}return t}const or="http://www.w3.org/1999/xlink";function lr(e,t,n,s,r,i=po(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(or,t.slice(6,t.length)):e.setAttributeNS(or,t,n):n==null||i&&!Fr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":ze(n)?String(n):n)}function cr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Ci(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Fr(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function ac(e,t,n,s){e.addEventListener(t,n,s)}function uc(e,t,n,s){e.removeEventListener(t,n,s)}const fr=Symbol("_vei");function dc(e,t,n,s,r=null){const i=e[fr]||(e[fr]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=hc(t);if(s){const u=i[t]=mc(s,r);ac(e,l,u,c)}else o&&(uc(e,l,o,c),i[t]=void 0)}}const ar=/(?:Once|Passive|Capture)$/;function hc(e){let t;if(ar.test(e)){t={};let s;for(;s=e.match(ar);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ct(e.slice(2)),t]}let $n=0;const pc=Promise.resolve(),gc=()=>$n||(pc.then(()=>$n=0),$n=Date.now());function mc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Fe(bc(s,n.value),t,5,[s])};return n.value=e,n.attached=gc(),n}function bc(e,t){if(D(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const ur=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,yc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?rc(e,s,o):t==="style"?cc(e,n,s):dn(t)?us(t)||dc(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):_c(e,t,s,o))?(cr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&lr(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Y(s))?cr(e,Ke(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),lr(e,t,s,o))};function _c(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ur(t)&&j(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return ur(t)&&Y(n)?!1:t in e}const wc=ce({patchProp:yc},nc);let dr;function xc(){return dr||(dr=Tl(wc))}const Ec=(...e)=>{const t=xc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Rc(s);if(!r)return;const i=t._component;!j(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,Sc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function Sc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Rc(e){return Y(e)?document.querySelector(e):e}function vi(e,t){return function(){return e.apply(t,arguments)}}const{toString:Tc}=Object.prototype,{getPrototypeOf:Fs}=Object,{iterator:xn,toStringTag:Pi}=Symbol,En=(e=>t=>{const n=Tc.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),we=e=>(e=e.toLowerCase(),t=>En(t)===e),Sn=e=>t=>typeof t===e,{isArray:mt}=Array,Bt=Sn("undefined");function Oc(e){return e!==null&&!Bt(e)&&e.constructor!==null&&!Bt(e.constructor)&&de(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Fi=we("ArrayBuffer");function Ac(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Fi(e.buffer),t}const Cc=Sn("string"),de=Sn("function"),Ni=Sn("number"),Rn=e=>e!==null&&typeof e=="object",vc=e=>e===!0||e===!1,en=e=>{if(En(e)!=="object")return!1;const t=Fs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Pi in e)&&!(xn in e)},Pc=we("Date"),Fc=we("File"),Nc=we("Blob"),Ic=we("FileList"),Dc=e=>Rn(e)&&de(e.pipe),Mc=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||de(e.append)&&((t=En(e))==="formdata"||t==="object"&&de(e.toString)&&e.toString()==="[object FormData]"))},Lc=we("URLSearchParams"),[Uc,jc,Bc,Hc]=["ReadableStream","Request","Response","Headers"].map(we),$c=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function qt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),mt(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let l;for(s=0;s<o;s++)l=i[s],t.call(null,e[l],l,e)}}function Ii(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const st=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Di=e=>!Bt(e)&&e!==st;function rs(){const{caseless:e}=Di(this)&&this||{},t={},n=(s,r)=>{const i=e&&Ii(t,r)||r;en(t[i])&&en(s)?t[i]=rs(t[i],s):en(s)?t[i]=rs({},s):mt(s)?t[i]=s.slice():t[i]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&qt(arguments[s],n);return t}const qc=(e,t,n,{allOwnKeys:s}={})=>(qt(t,(r,i)=>{n&&de(r)?e[i]=vi(r,n):e[i]=r},{allOwnKeys:s}),e),kc=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Vc=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Kc=(e,t,n,s)=>{let r,i,o;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),i=r.length;i-- >0;)o=r[i],(!s||s(o,e,t))&&!l[o]&&(t[o]=e[o],l[o]=!0);e=n!==!1&&Fs(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Wc=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},zc=e=>{if(!e)return null;if(mt(e))return e;let t=e.length;if(!Ni(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Jc=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Fs(Uint8Array)),Gc=(e,t)=>{const s=(e&&e[xn]).call(e);let r;for(;(r=s.next())&&!r.done;){const i=r.value;t.call(e,i[0],i[1])}},Xc=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Yc=we("HTMLFormElement"),Zc=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),hr=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Qc=we("RegExp"),Mi=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};qt(n,(r,i)=>{let o;(o=t(r,i,e))!==!1&&(s[i]=o||r)}),Object.defineProperties(e,s)},ef=e=>{Mi(e,(t,n)=>{if(de(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(de(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},tf=(e,t)=>{const n={},s=r=>{r.forEach(i=>{n[i]=!0})};return mt(e)?s(e):s(String(e).split(t)),n},nf=()=>{},sf=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function rf(e){return!!(e&&de(e.append)&&e[Pi]==="FormData"&&e[xn])}const of=e=>{const t=new Array(10),n=(s,r)=>{if(Rn(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const i=mt(s)?[]:{};return qt(s,(o,l)=>{const c=n(o,r+1);!Bt(c)&&(i[l]=c)}),t[r]=void 0,i}}return s};return n(e,0)},lf=we("AsyncFunction"),cf=e=>e&&(Rn(e)||de(e))&&de(e.then)&&de(e.catch),Li=((e,t)=>e?setImmediate:t?((n,s)=>(st.addEventListener("message",({source:r,data:i})=>{r===st&&i===n&&s.length&&s.shift()()},!1),r=>{s.push(r),st.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",de(st.postMessage)),ff=typeof queueMicrotask<"u"?queueMicrotask.bind(st):typeof process<"u"&&process.nextTick||Li,af=e=>e!=null&&de(e[xn]),p={isArray:mt,isArrayBuffer:Fi,isBuffer:Oc,isFormData:Mc,isArrayBufferView:Ac,isString:Cc,isNumber:Ni,isBoolean:vc,isObject:Rn,isPlainObject:en,isReadableStream:Uc,isRequest:jc,isResponse:Bc,isHeaders:Hc,isUndefined:Bt,isDate:Pc,isFile:Fc,isBlob:Nc,isRegExp:Qc,isFunction:de,isStream:Dc,isURLSearchParams:Lc,isTypedArray:Jc,isFileList:Ic,forEach:qt,merge:rs,extend:qc,trim:$c,stripBOM:kc,inherits:Vc,toFlatObject:Kc,kindOf:En,kindOfTest:we,endsWith:Wc,toArray:zc,forEachEntry:Gc,matchAll:Xc,isHTMLForm:Yc,hasOwnProperty:hr,hasOwnProp:hr,reduceDescriptors:Mi,freezeMethods:ef,toObjectSet:tf,toCamelCase:Zc,noop:nf,toFiniteNumber:sf,findKey:Ii,global:st,isContextDefined:Di,isSpecCompliantForm:rf,toJSONObject:of,isAsyncFn:lf,isThenable:cf,setImmediate:Li,asap:ff,isIterable:af};function L(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}p.inherits(L,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:p.toJSONObject(this.config),code:this.code,status:this.status}}});const Ui=L.prototype,ji={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ji[e]={value:e}});Object.defineProperties(L,ji);Object.defineProperty(Ui,"isAxiosError",{value:!0});L.from=(e,t,n,s,r,i)=>{const o=Object.create(Ui);return p.toFlatObject(e,o,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),L.call(o,e.message,t,n,s,r),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const uf=null;function is(e){return p.isPlainObject(e)||p.isArray(e)}function Bi(e){return p.endsWith(e,"[]")?e.slice(0,-2):e}function pr(e,t,n){return e?e.concat(t).map(function(r,i){return r=Bi(r),!n&&i?"["+r+"]":r}).join(n?".":""):t}function df(e){return p.isArray(e)&&!e.some(is)}const hf=p.toFlatObject(p,{},null,function(t){return/^is[A-Z]/.test(t)});function Tn(e,t,n){if(!p.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=p.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,O){return!p.isUndefined(O[E])});const s=n.metaTokens,r=n.visitor||f,i=n.dots,o=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&p.isSpecCompliantForm(t);if(!p.isFunction(r))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(p.isDate(m))return m.toISOString();if(p.isBoolean(m))return m.toString();if(!c&&p.isBlob(m))throw new L("Blob is not supported. Use a Buffer instead.");return p.isArrayBuffer(m)||p.isTypedArray(m)?c&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function f(m,E,O){let N=m;if(m&&!O&&typeof m=="object"){if(p.endsWith(E,"{}"))E=s?E:E.slice(0,-2),m=JSON.stringify(m);else if(p.isArray(m)&&df(m)||(p.isFileList(m)||p.endsWith(E,"[]"))&&(N=p.toArray(m)))return E=Bi(E),N.forEach(function(U,v){!(p.isUndefined(U)||U===null)&&t.append(o===!0?pr([E],v,i):o===null?E:E+"[]",u(U))}),!1}return is(m)?!0:(t.append(pr(O,E,i),u(m)),!1)}const h=[],y=Object.assign(hf,{defaultVisitor:f,convertValue:u,isVisitable:is});function R(m,E){if(!p.isUndefined(m)){if(h.indexOf(m)!==-1)throw Error("Circular reference detected in "+E.join("."));h.push(m),p.forEach(m,function(N,M){(!(p.isUndefined(N)||N===null)&&r.call(t,N,p.isString(M)?M.trim():M,E,y))===!0&&R(N,E?E.concat(M):[M])}),h.pop()}}if(!p.isObject(e))throw new TypeError("data must be an object");return R(e),t}function gr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Ns(e,t){this._pairs=[],e&&Tn(e,this,t)}const Hi=Ns.prototype;Hi.append=function(t,n){this._pairs.push([t,n])};Hi.toString=function(t){const n=t?function(s){return t.call(this,s,gr)}:gr;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function pf(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $i(e,t,n){if(!t)return e;const s=n&&n.encode||pf;p.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let i;if(r?i=r(t,n):i=p.isURLSearchParams(t)?t.toString():new Ns(t,n).toString(s),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class mr{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){p.forEach(this.handlers,function(s){s!==null&&t(s)})}}const qi={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},gf=typeof URLSearchParams<"u"?URLSearchParams:Ns,mf=typeof FormData<"u"?FormData:null,bf=typeof Blob<"u"?Blob:null,yf={isBrowser:!0,classes:{URLSearchParams:gf,FormData:mf,Blob:bf},protocols:["http","https","file","blob","url","data"]},Is=typeof window<"u"&&typeof document<"u",os=typeof navigator=="object"&&navigator||void 0,_f=Is&&(!os||["ReactNative","NativeScript","NS"].indexOf(os.product)<0),wf=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",xf=Is&&window.location.href||"http://localhost",Ef=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Is,hasStandardBrowserEnv:_f,hasStandardBrowserWebWorkerEnv:wf,navigator:os,origin:xf},Symbol.toStringTag,{value:"Module"})),te={...Ef,...yf};function Sf(e,t){return Tn(e,new te.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,i){return te.isNode&&p.isBuffer(n)?(this.append(s,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Rf(e){return p.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tf(e){const t={},n=Object.keys(e);let s;const r=n.length;let i;for(s=0;s<r;s++)i=n[s],t[i]=e[i];return t}function ki(e){function t(n,s,r,i){let o=n[i++];if(o==="__proto__")return!0;const l=Number.isFinite(+o),c=i>=n.length;return o=!o&&p.isArray(r)?r.length:o,c?(p.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!l):((!r[o]||!p.isObject(r[o]))&&(r[o]=[]),t(n,s,r[o],i)&&p.isArray(r[o])&&(r[o]=Tf(r[o])),!l)}if(p.isFormData(e)&&p.isFunction(e.entries)){const n={};return p.forEachEntry(e,(s,r)=>{t(Rf(s),r,n,0)}),n}return null}function Of(e,t,n){if(p.isString(e))try{return(t||JSON.parse)(e),p.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const kt={transitional:qi,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,i=p.isObject(t);if(i&&p.isHTMLForm(t)&&(t=new FormData(t)),p.isFormData(t))return r?JSON.stringify(ki(t)):t;if(p.isArrayBuffer(t)||p.isBuffer(t)||p.isStream(t)||p.isFile(t)||p.isBlob(t)||p.isReadableStream(t))return t;if(p.isArrayBufferView(t))return t.buffer;if(p.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Sf(t,this.formSerializer).toString();if((l=p.isFileList(t))||s.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Tn(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||r?(n.setContentType("application/json",!1),Of(t)):t}],transformResponse:[function(t){const n=this.transitional||kt.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(p.isResponse(t)||p.isReadableStream(t))return t;if(t&&p.isString(t)&&(s&&!this.responseType||r)){const o=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(o)throw l.name==="SyntaxError"?L.from(l,L.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:te.classes.FormData,Blob:te.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};p.forEach(["delete","get","head","post","put","patch"],e=>{kt.headers[e]={}});const Af=p.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Cf=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(o){r=o.indexOf(":"),n=o.substring(0,r).trim().toLowerCase(),s=o.substring(r+1).trim(),!(!n||t[n]&&Af[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},br=Symbol("internals");function Rt(e){return e&&String(e).trim().toLowerCase()}function tn(e){return e===!1||e==null?e:p.isArray(e)?e.map(tn):String(e)}function vf(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Pf=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function qn(e,t,n,s,r){if(p.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!p.isString(t)){if(p.isString(s))return t.indexOf(s)!==-1;if(p.isRegExp(s))return s.test(t)}}function Ff(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Nf(e,t){const n=p.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,i,o){return this[s].call(this,t,r,i,o)},configurable:!0})})}let he=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function i(l,c,u){const f=Rt(c);if(!f)throw new Error("header name must be a non-empty string");const h=p.findKey(r,f);(!h||r[h]===void 0||u===!0||u===void 0&&r[h]!==!1)&&(r[h||c]=tn(l))}const o=(l,c)=>p.forEach(l,(u,f)=>i(u,f,c));if(p.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(p.isString(t)&&(t=t.trim())&&!Pf(t))o(Cf(t),n);else if(p.isObject(t)&&p.isIterable(t)){let l={},c,u;for(const f of t){if(!p.isArray(f))throw TypeError("Object iterator must return a key-value pair");l[u=f[0]]=(c=l[u])?p.isArray(c)?[...c,f[1]]:[c,f[1]]:f[1]}o(l,n)}else t!=null&&i(n,t,s);return this}get(t,n){if(t=Rt(t),t){const s=p.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return vf(r);if(p.isFunction(n))return n.call(this,r,s);if(p.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Rt(t),t){const s=p.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||qn(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function i(o){if(o=Rt(o),o){const l=p.findKey(s,o);l&&(!n||qn(s,s[l],l,n))&&(delete s[l],r=!0)}}return p.isArray(t)?t.forEach(i):i(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const i=n[s];(!t||qn(this,this[i],i,t,!0))&&(delete this[i],r=!0)}return r}normalize(t){const n=this,s={};return p.forEach(this,(r,i)=>{const o=p.findKey(s,i);if(o){n[o]=tn(r),delete n[i];return}const l=t?Ff(i):String(i).trim();l!==i&&delete n[i],n[l]=tn(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return p.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&p.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[br]=this[br]={accessors:{}}).accessors,r=this.prototype;function i(o){const l=Rt(o);s[l]||(Nf(r,o),s[l]=!0)}return p.isArray(t)?t.forEach(i):i(t),this}};he.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);p.reduceDescriptors(he.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});p.freezeMethods(he);function kn(e,t){const n=this||kt,s=t||n,r=he.from(s.headers);let i=s.data;return p.forEach(e,function(l){i=l.call(n,i,r.normalize(),t?t.status:void 0)}),r.normalize(),i}function Vi(e){return!!(e&&e.__CANCEL__)}function bt(e,t,n){L.call(this,e??"canceled",L.ERR_CANCELED,t,n),this.name="CanceledError"}p.inherits(bt,L,{__CANCEL__:!0});function Ki(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new L("Request failed with status code "+n.status,[L.ERR_BAD_REQUEST,L.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function If(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Df(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,i=0,o;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),f=s[i];o||(o=u),n[r]=c,s[r]=u;let h=i,y=0;for(;h!==r;)y+=n[h++],h=h%e;if(r=(r+1)%e,r===i&&(i=(i+1)%e),u-o<t)return;const R=f&&u-f;return R?Math.round(y*1e3/R):void 0}}function Mf(e,t){let n=0,s=1e3/t,r,i;const o=(u,f=Date.now())=>{n=f,r=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const f=Date.now(),h=f-n;h>=s?o(u,f):(r=u,i||(i=setTimeout(()=>{i=null,o(r)},s-h)))},()=>r&&o(r)]}const an=(e,t,n=3)=>{let s=0;const r=Df(50,250);return Mf(i=>{const o=i.loaded,l=i.lengthComputable?i.total:void 0,c=o-s,u=r(c),f=o<=l;s=o;const h={loaded:o,total:l,progress:l?o/l:void 0,bytes:c,rate:u||void 0,estimated:u&&l&&f?(l-o)/u:void 0,event:i,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(h)},n)},yr=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},_r=e=>(...t)=>p.asap(()=>e(...t)),Lf=te.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,te.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(te.origin),te.navigator&&/(msie|trident)/i.test(te.navigator.userAgent)):()=>!0,Uf=te.hasStandardBrowserEnv?{write(e,t,n,s,r,i){const o=[e+"="+encodeURIComponent(t)];p.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),p.isString(s)&&o.push("path="+s),p.isString(r)&&o.push("domain="+r),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function jf(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bf(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Wi(e,t,n){let s=!jf(t);return e&&(s||n==!1)?Bf(e,t):t}const wr=e=>e instanceof he?{...e}:e;function lt(e,t){t=t||{};const n={};function s(u,f,h,y){return p.isPlainObject(u)&&p.isPlainObject(f)?p.merge.call({caseless:y},u,f):p.isPlainObject(f)?p.merge({},f):p.isArray(f)?f.slice():f}function r(u,f,h,y){if(p.isUndefined(f)){if(!p.isUndefined(u))return s(void 0,u,h,y)}else return s(u,f,h,y)}function i(u,f){if(!p.isUndefined(f))return s(void 0,f)}function o(u,f){if(p.isUndefined(f)){if(!p.isUndefined(u))return s(void 0,u)}else return s(void 0,f)}function l(u,f,h){if(h in t)return s(u,f);if(h in e)return s(void 0,u)}const c={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:l,headers:(u,f,h)=>r(wr(u),wr(f),h,!0)};return p.forEach(Object.keys(Object.assign({},e,t)),function(f){const h=c[f]||r,y=h(e[f],t[f],f);p.isUndefined(y)&&h!==l||(n[f]=y)}),n}const zi=e=>{const t=lt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:i,headers:o,auth:l}=t;t.headers=o=he.from(o),t.url=$i(Wi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&o.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(p.isFormData(n)){if(te.hasStandardBrowserEnv||te.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((c=o.getContentType())!==!1){const[u,...f]=c?c.split(";").map(h=>h.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...f].join("; "))}}if(te.hasStandardBrowserEnv&&(s&&p.isFunction(s)&&(s=s(t)),s||s!==!1&&Lf(t.url))){const u=r&&i&&Uf.read(i);u&&o.set(r,u)}return t},Hf=typeof XMLHttpRequest<"u",$f=Hf&&function(e){return new Promise(function(n,s){const r=zi(e);let i=r.data;const o=he.from(r.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:u}=r,f,h,y,R,m;function E(){R&&R(),m&&m(),r.cancelToken&&r.cancelToken.unsubscribe(f),r.signal&&r.signal.removeEventListener("abort",f)}let O=new XMLHttpRequest;O.open(r.method.toUpperCase(),r.url,!0),O.timeout=r.timeout;function N(){if(!O)return;const U=he.from("getAllResponseHeaders"in O&&O.getAllResponseHeaders()),B={data:!l||l==="text"||l==="json"?O.responseText:O.response,status:O.status,statusText:O.statusText,headers:U,config:e,request:O};Ki(function(Z){n(Z),E()},function(Z){s(Z),E()},B),O=null}"onloadend"in O?O.onloadend=N:O.onreadystatechange=function(){!O||O.readyState!==4||O.status===0&&!(O.responseURL&&O.responseURL.indexOf("file:")===0)||setTimeout(N)},O.onabort=function(){O&&(s(new L("Request aborted",L.ECONNABORTED,e,O)),O=null)},O.onerror=function(){s(new L("Network Error",L.ERR_NETWORK,e,O)),O=null},O.ontimeout=function(){let v=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const B=r.transitional||qi;r.timeoutErrorMessage&&(v=r.timeoutErrorMessage),s(new L(v,B.clarifyTimeoutError?L.ETIMEDOUT:L.ECONNABORTED,e,O)),O=null},i===void 0&&o.setContentType(null),"setRequestHeader"in O&&p.forEach(o.toJSON(),function(v,B){O.setRequestHeader(B,v)}),p.isUndefined(r.withCredentials)||(O.withCredentials=!!r.withCredentials),l&&l!=="json"&&(O.responseType=r.responseType),u&&([y,m]=an(u,!0),O.addEventListener("progress",y)),c&&O.upload&&([h,R]=an(c),O.upload.addEventListener("progress",h),O.upload.addEventListener("loadend",R)),(r.cancelToken||r.signal)&&(f=U=>{O&&(s(!U||U.type?new bt(null,e,O):U),O.abort(),O=null)},r.cancelToken&&r.cancelToken.subscribe(f),r.signal&&(r.signal.aborted?f():r.signal.addEventListener("abort",f)));const M=If(r.url);if(M&&te.protocols.indexOf(M)===-1){s(new L("Unsupported protocol "+M+":",L.ERR_BAD_REQUEST,e));return}O.send(i||null)})},qf=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const i=function(u){if(!r){r=!0,l();const f=u instanceof Error?u:this.reason;s.abort(f instanceof L?f:new bt(f instanceof Error?f.message:f))}};let o=t&&setTimeout(()=>{o=null,i(new L(`timeout ${t} of ms exceeded`,L.ETIMEDOUT))},t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:c}=s;return c.unsubscribe=()=>p.asap(l),c}},kf=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},Vf=async function*(e,t){for await(const n of Kf(e))yield*kf(n,t)},Kf=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},xr=(e,t,n,s)=>{const r=Vf(e,t);let i=0,o,l=c=>{o||(o=!0,s&&s(c))};return new ReadableStream({async pull(c){try{const{done:u,value:f}=await r.next();if(u){l(),c.close();return}let h=f.byteLength;if(n){let y=i+=h;n(y)}c.enqueue(new Uint8Array(f))}catch(u){throw l(u),u}},cancel(c){return l(c),r.return()}},{highWaterMark:2})},On=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ji=On&&typeof ReadableStream=="function",Wf=On&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Gi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},zf=Ji&&Gi(()=>{let e=!1;const t=new Request(te.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Er=64*1024,ls=Ji&&Gi(()=>p.isReadableStream(new Response("").body)),un={stream:ls&&(e=>e.body)};On&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!un[t]&&(un[t]=p.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new L(`Response type '${t}' is not supported`,L.ERR_NOT_SUPPORT,s)})})})(new Response);const Jf=async e=>{if(e==null)return 0;if(p.isBlob(e))return e.size;if(p.isSpecCompliantForm(e))return(await new Request(te.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(p.isArrayBufferView(e)||p.isArrayBuffer(e))return e.byteLength;if(p.isURLSearchParams(e)&&(e=e+""),p.isString(e))return(await Wf(e)).byteLength},Gf=async(e,t)=>{const n=p.toFiniteNumber(e.getContentLength());return n??Jf(t)},Xf=On&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:i,timeout:o,onDownloadProgress:l,onUploadProgress:c,responseType:u,headers:f,withCredentials:h="same-origin",fetchOptions:y}=zi(e);u=u?(u+"").toLowerCase():"text";let R=qf([r,i&&i.toAbortSignal()],o),m;const E=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let O;try{if(c&&zf&&n!=="get"&&n!=="head"&&(O=await Gf(f,s))!==0){let B=new Request(t,{method:"POST",body:s,duplex:"half"}),Q;if(p.isFormData(s)&&(Q=B.headers.get("content-type"))&&f.setContentType(Q),B.body){const[Z,pe]=yr(O,an(_r(c)));s=xr(B.body,Er,Z,pe)}}p.isString(h)||(h=h?"include":"omit");const N="credentials"in Request.prototype;m=new Request(t,{...y,signal:R,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:s,duplex:"half",credentials:N?h:void 0});let M=await fetch(m,y);const U=ls&&(u==="stream"||u==="response");if(ls&&(l||U&&E)){const B={};["status","statusText","headers"].forEach(Je=>{B[Je]=M[Je]});const Q=p.toFiniteNumber(M.headers.get("content-length")),[Z,pe]=l&&yr(Q,an(_r(l),!0))||[];M=new Response(xr(M.body,Er,Z,()=>{pe&&pe(),E&&E()}),B)}u=u||"text";let v=await un[p.findKey(un,u)||"text"](M,e);return!U&&E&&E(),await new Promise((B,Q)=>{Ki(B,Q,{data:v,headers:he.from(M.headers),status:M.status,statusText:M.statusText,config:e,request:m})})}catch(N){throw E&&E(),N&&N.name==="TypeError"&&/Load failed|fetch/i.test(N.message)?Object.assign(new L("Network Error",L.ERR_NETWORK,e,m),{cause:N.cause||N}):L.from(N,N&&N.code,e,m)}}),cs={http:uf,xhr:$f,fetch:Xf};p.forEach(cs,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Sr=e=>`- ${e}`,Yf=e=>p.isFunction(e)||e===null||e===!1,Xi={getAdapter:e=>{e=p.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let i=0;i<t;i++){n=e[i];let o;if(s=n,!Yf(n)&&(s=cs[(o=String(n)).toLowerCase()],s===void 0))throw new L(`Unknown adapter '${o}'`);if(s)break;r[o||"#"+i]=s}if(!s){const i=Object.entries(r).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(Sr).join(`
`):" "+Sr(i[0]):"as no adapter specified";throw new L("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:cs};function Vn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new bt(null,e)}function Rr(e){return Vn(e),e.headers=he.from(e.headers),e.data=kn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Xi.getAdapter(e.adapter||kt.adapter)(e).then(function(s){return Vn(e),s.data=kn.call(e,e.transformResponse,s),s.headers=he.from(s.headers),s},function(s){return Vi(s)||(Vn(e),s&&s.response&&(s.response.data=kn.call(e,e.transformResponse,s.response),s.response.headers=he.from(s.response.headers))),Promise.reject(s)})}const Yi="1.10.0",An={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{An[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Tr={};An.transitional=function(t,n,s){function r(i,o){return"[Axios v"+Yi+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,l)=>{if(t===!1)throw new L(r(o," has been removed"+(n?" in "+n:"")),L.ERR_DEPRECATED);return n&&!Tr[o]&&(Tr[o]=!0,console.warn(r(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,l):!0}};An.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Zf(e,t,n){if(typeof e!="object")throw new L("options must be an object",L.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const i=s[r],o=t[i];if(o){const l=e[i],c=l===void 0||o(l,i,e);if(c!==!0)throw new L("option "+i+" must be "+c,L.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new L("Unknown option "+i,L.ERR_BAD_OPTION)}}const nn={assertOptions:Zf,validators:An},Oe=nn.validators;let it=class{constructor(t){this.defaults=t||{},this.interceptors={request:new mr,response:new mr}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const i=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=lt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:i}=n;s!==void 0&&nn.assertOptions(s,{silentJSONParsing:Oe.transitional(Oe.boolean),forcedJSONParsing:Oe.transitional(Oe.boolean),clarifyTimeoutError:Oe.transitional(Oe.boolean)},!1),r!=null&&(p.isFunction(r)?n.paramsSerializer={serialize:r}:nn.assertOptions(r,{encode:Oe.function,serialize:Oe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),nn.assertOptions(n,{baseUrl:Oe.spelling("baseURL"),withXsrfToken:Oe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&p.merge(i.common,i[n.method]);i&&p.forEach(["delete","get","head","post","put","patch","common"],m=>{delete i[m]}),n.headers=he.concat(o,i);const l=[];let c=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(n)===!1||(c=c&&E.synchronous,l.unshift(E.fulfilled,E.rejected))});const u=[];this.interceptors.response.forEach(function(E){u.push(E.fulfilled,E.rejected)});let f,h=0,y;if(!c){const m=[Rr.bind(this),void 0];for(m.unshift.apply(m,l),m.push.apply(m,u),y=m.length,f=Promise.resolve(n);h<y;)f=f.then(m[h++],m[h++]);return f}y=l.length;let R=n;for(h=0;h<y;){const m=l[h++],E=l[h++];try{R=m(R)}catch(O){E.call(this,O);break}}try{f=Rr.call(this,R)}catch(m){return Promise.reject(m)}for(h=0,y=u.length;h<y;)f=f.then(u[h++],u[h++]);return f}getUri(t){t=lt(this.defaults,t);const n=Wi(t.baseURL,t.url,t.allowAbsoluteUrls);return $i(n,t.params,t.paramsSerializer)}};p.forEach(["delete","get","head","options"],function(t){it.prototype[t]=function(n,s){return this.request(lt(s||{},{method:t,url:n,data:(s||{}).data}))}});p.forEach(["post","put","patch"],function(t){function n(s){return function(i,o,l){return this.request(lt(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}it.prototype[t]=n(),it.prototype[t+"Form"]=n(!0)});let Qf=class Zi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const s=this;this.promise.then(r=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](r);s._listeners=null}),this.promise.then=r=>{let i;const o=new Promise(l=>{s.subscribe(l),i=l}).then(r);return o.cancel=function(){s.unsubscribe(i)},o},t(function(i,o,l){s.reason||(s.reason=new bt(i,o,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Zi(function(r){t=r}),cancel:t}}};function ea(e){return function(n){return e.apply(null,n)}}function ta(e){return p.isObject(e)&&e.isAxiosError===!0}const fs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(fs).forEach(([e,t])=>{fs[t]=e});function Qi(e){const t=new it(e),n=vi(it.prototype.request,t);return p.extend(n,it.prototype,t,{allOwnKeys:!0}),p.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Qi(lt(e,r))},n}const X=Qi(kt);X.Axios=it;X.CanceledError=bt;X.CancelToken=Qf;X.isCancel=Vi;X.VERSION=Yi;X.toFormData=Tn;X.AxiosError=L;X.Cancel=X.CanceledError;X.all=function(t){return Promise.all(t)};X.spread=ea;X.isAxiosError=ta;X.mergeConfig=lt;X.AxiosHeaders=he;X.formToJSON=e=>ki(p.isHTMLForm(e)?new FormData(e):e);X.getAdapter=Xi.getAdapter;X.HttpStatusCode=fs;X.default=X;const{Axios:_a,AxiosError:wa,CanceledError:xa,isCancel:Ea,CancelToken:Sa,VERSION:Ra,all:Ta,Cancel:Oa,isAxiosError:Aa,spread:Ca,toFormData:va,AxiosHeaders:Pa,HttpStatusCode:Fa,formToJSON:Na,getAdapter:Ia,mergeConfig:Da}=X;function na(){const e=Ze(null),t=Ze(null),n=Ze(null),s=Ze(!1),r=Ze(!1),i=Ze(""),o=Ze(3e4);let l=null;const c="https://api.example.com/gold/price",u=async()=>{await new Promise(N=>setTimeout(N,500));const m=1980,E=(Math.random()-.5)*20,O=m+E;if(Math.random()<.05)throw new Error("API temporarily unavailable");return{price:parseFloat(O.toFixed(2))}},f=async()=>{s.value=!0,r.value=!1,i.value="";try{e.value!==null&&(t.value=e.value);let m;try{m=await X.get(c,{timeout:5e3})}catch{console.log("Real API not available, using mock data"),m={data:await u()}}const E=m.data.price;if(typeof E=="number"&&E>0)e.value=E,t.value!==null&&(E>t.value?n.value="up":E<t.value?n.value="down":n.value=null);else throw new Error("Invalid price data received")}catch(m){console.error("Error fetching gold price:",m),r.value=!0,i.value=m.message||"Failed to fetch price",n.value=null}finally{s.value=!1}},h=async()=>{try{if(window.electronAPI){const m=await window.electronAPI.getConfig();o.value=m.updateInterval||3e4}}catch{console.log("Could not load config, using default interval")}y(),l=setInterval(f,o.value)},y=()=>{l&&(clearInterval(l),l=null)};return{price:e,previousPrice:t,priceChange:n,isLoading:s,hasError:r,errorMessage:i,updateInterval:o,fetchPrice:f,startAutoUpdate:h,stopAutoUpdate:y,updateConfig:async m=>{try{window.electronAPI&&(await window.electronAPI.saveConfig(m),m.updateInterval&&m.updateInterval!==o.value&&(o.value=m.updateInterval,l&&(y(),h())))}catch(E){console.error("Error updating config:",E)}}}}const sa=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},ra={name:"App",setup(){const{price:e,priceChange:t,isLoading:n,hasError:s,errorMessage:r,fetchPrice:i,startAutoUpdate:o,stopAutoUpdate:l}=na(),c=ns(()=>e.value?e.value.toFixed(2):"0.00"),u=ns(()=>t.value==="up"?"price-up":t.value==="down"?"price-down":"");return ii(async()=>{await i(),o()}),Os(()=>{l()}),{price:e,priceChange:t,isLoading:n,hasError:s,errorMessage:r,formattedPrice:c,priceChangeClass:u}}},ia={class:"widget-container"},oa={key:0,class:"loading"},la=["title"],ca={key:2,class:"price-display"},fa={key:0,class:"price-change"},aa={key:0,class:"price-up"},ua={key:1,class:"price-down"};function da(e,t,n,s,r,i){return He(),tt("div",ia,[s.isLoading?(He(),tt("div",oa," ... ")):s.hasError?(He(),tt("div",{key:1,class:"error",title:s.errorMessage}," ❗ ",8,la)):(He(),tt("div",ca,[s.priceChange?(He(),tt("div",fa,[s.priceChange==="up"?(He(),tt("span",aa,"↑")):s.priceChange==="down"?(He(),tt("span",ua,"↓")):Zs("",!0)])):Zs("",!0),cn("div",{class:mn(["price-value",s.priceChangeClass])}," $"+Ir(s.formattedPrice),3),t[0]||(t[0]=cn("div",{class:"price-unit"}," USD/oz ",-1))]))])}const ha=sa(ra,[["render",da]]),pa=Ec(ha);pa.mount("#app");
