const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ipc<PERSON>ain, screen } = require('electron')
const path = require('path')
const fs = require('fs')

// Keep a global reference of the window objects
let mainWindow = null
let settingsWindow = null
let tray = null

// Configuration file path
const configPath = path.join(app.getPath('userData'), 'config.json')

// Default configuration
const defaultConfig = {
  windowPosition: { x: 100, y: 100 },
  updateInterval: 30000, // 30 seconds
  isVisible: true
}

// Load configuration
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const data = fs.readFileSync(configPath, 'utf8')
      return { ...defaultConfig, ...JSON.parse(data) }
    }
  } catch (error) {
    console.error('Error loading config:', error)
  }
  return defaultConfig
}

// Save configuration
function saveConfig(config) {
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
  } catch (error) {
    console.error('Error saving config:', error)
  }
}

function createMainWindow() {
  const config = loadConfig()
  
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 80,
    height: 80,
    x: config.windowPosition.x,
    y: config.windowPosition.y,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  })

  // Load the app
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173')
    // mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../../dist/index.html'))
  }

  // Make window draggable
  mainWindow.setIgnoreMouseEvents(false)

  // Save position when window is moved
  mainWindow.on('moved', () => {
    const position = mainWindow.getPosition()
    const config = loadConfig()
    config.windowPosition = { x: position[0], y: position[1] }
    saveConfig(config)
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Show/hide based on config
  if (config.isVisible) {
    mainWindow.show()
  } else {
    mainWindow.hide()
  }
}

function createSettingsWindow() {
  if (settingsWindow) {
    settingsWindow.focus()
    return
  }

  settingsWindow = new BrowserWindow({
    width: 400,
    height: 300,
    resizable: false,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  })

  // Load settings page
  if (process.env.NODE_ENV === 'development') {
    settingsWindow.loadURL('http://localhost:5173/settings.html')
  } else {
    settingsWindow.loadFile(path.join(__dirname, '../../dist/settings.html'))
  }

  settingsWindow.on('closed', () => {
    settingsWindow = null
  })
}

function createTray() {
  // Create tray icon - use a simple built-in icon for now
  // In production, you would use a proper icon file
  let iconPath
  try {
    iconPath = path.join(__dirname, '../../assets/tray-icon.png')
    // Check if file exists, if not use a fallback
    if (!fs.existsSync(iconPath)) {
      // Use a simple text-based icon as fallback
      iconPath = path.join(__dirname, '../../assets/icon.svg')
    }
  } catch (error) {
    console.log('Icon file not found, using default')
    // Create a minimal icon programmatically if needed
    iconPath = null
  }

  // For now, we'll skip the tray icon if no proper icon is available
  // and just create the main window
  if (iconPath && fs.existsSync(iconPath)) {
    tray = new Tray(iconPath)
  } else {
    console.log('Tray icon not available, skipping tray creation')
    return
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show/Hide Widget',
      click: () => {
        if (mainWindow) {
          if (mainWindow.isVisible()) {
            mainWindow.hide()
            const config = loadConfig()
            config.isVisible = false
            saveConfig(config)
          } else {
            mainWindow.show()
            const config = loadConfig()
            config.isVisible = true
            saveConfig(config)
          }
        }
      }
    },
    {
      label: 'Settings...',
      click: () => {
        createSettingsWindow()
      }
    },
    { type: 'separator' },
    {
      label: 'Exit',
      click: () => {
        app.quit()
      }
    }
  ])

  tray.setToolTip('Gold Price Widget')
  tray.setContextMenu(contextMenu)

  // Left click to toggle widget
  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide()
      } else {
        mainWindow.show()
      }
    }
  })
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow()
  createTray()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow()
    }
  })
})

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    // Don't quit, just hide to tray
  }
})

app.on('before-quit', () => {
  // Save final configuration before quitting
  if (mainWindow) {
    const position = mainWindow.getPosition()
    const config = loadConfig()
    config.windowPosition = { x: position[0], y: position[1] }
    config.isVisible = mainWindow.isVisible()
    saveConfig(config)
  }
})

// IPC handlers
ipcMain.handle('get-config', () => {
  return loadConfig()
})

ipcMain.handle('save-config', (event, newConfig) => {
  const config = loadConfig()
  const updatedConfig = { ...config, ...newConfig }
  saveConfig(updatedConfig)
  return updatedConfig
})

ipcMain.handle('get-gold-price', async () => {
  // This will be implemented in the renderer process
  // Just return a placeholder for now
  return { price: 1980.50, change: 'up' }
})
