// Simple script to create basic icon files for the application
const fs = require('fs')
const path = require('path')

// Create a simple SVG icon that we can convert to different formats
const svgIcon = `
<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <circle cx="8" cy="8" r="7" fill="#FFD700" stroke="#B8860B" stroke-width="1"/>
  <text x="8" y="11" text-anchor="middle" font-family="Arial" font-size="8" font-weight="bold" fill="#B8860B">$</text>
</svg>
`

// Create assets directory if it doesn't exist
const assetsDir = path.join(__dirname, 'assets')
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true })
}

// Save SVG file
fs.writeFileSync(path.join(assetsDir, 'icon.svg'), svgIcon)

console.log('Basic icon files created in assets directory')
console.log('Note: For production, you should create proper .ico, .icns, and .png files')
console.log('You can use online converters or tools like electron-icon-builder to generate proper icons')
