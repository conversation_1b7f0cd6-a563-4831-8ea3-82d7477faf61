const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Configuration methods
  getConfig: () => ipcRenderer.invoke('get-config'),
  saveConfig: (config) => ipcRenderer.invoke('save-config', config),
  
  // Gold price methods (placeholder for future API integration)
  getGoldPrice: () => ipcRenderer.invoke('get-gold-price'),
  
  // Window methods
  minimize: () => ipcRenderer.invoke('minimize-window'),
  close: () => ipcRenderer.invoke('close-window'),
  
  // Platform info
  platform: process.platform
})
