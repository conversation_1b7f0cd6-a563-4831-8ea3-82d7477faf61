需求标题： 开发一款跨平台的黄金价格桌面小组件 (Desktop Widget)
1. 核心目标
使用 Electron 框架，结合 Vue，开发一款独立的、跨平台（Windows, macOS, Linux）的桌面应用程序。该应用以一个“悬浮球”小组件的形式存在，始终显示在屏幕顶层，用于实时监控黄金价格。
2. 技术选型
● 桌面应用框架：Electron (因其成熟的生态和广泛的社区支持)。
● 前端 UI 框架： 在应用窗口内部，使用 Vue 3 进行界面渲染。
● 数据请求： axios 或 Electron 提供的原生 HTTP 客户端。
● 样式： CSS Modules 或 Tailwind CSS，确保界面美观且易于维护。
3. 应用规格 (Application Specifications)
3.1. 窗口行为与交互
● 主窗口（悬浮球）：
  ○ 外观： 一个 无边框 (frameless)、背景透明 的窗口。窗口内的主要内容是一个直径 80px 的圆形区域，使其看起来像一个球。
  ○ 置顶： 窗口必须始终保持在所有其他应用窗口的 最顶层 (always-on-top)。
  ○ 拖拽： 用户可以通过鼠标按住悬浮球在整个桌面上自由拖动。
  ○ 穿透点击（可选）： 只有圆形内容区域可响应鼠标事件（拖拽、点击），透明区域应“鼠标穿透”，允许用户点击下方的其他窗口。
● 位置持久化：
  ○ 应用关闭时，必须 保存悬浮球在屏幕上的最后位置 (X, Y 坐标)。
  ○ 应用下次启动时，悬浮球应在之前保存的位置恢复。数据可以保存在本地的 JSON 配置文件中。
● 交互反馈：
  ○ 鼠标悬停在圆形区域时，有轻微放大 (scale: 1.05) 的动效。
3.2. 数据展示（与网页版类似，但在此重申）
● 价格显示： 清晰显示美元/盎司的黄金价格，保留两位小数（如 $1980.50）。价格下方用小号字体显示 "USD/oz"。
● 价格变化指示：
  ○ 上涨时，价格变为 绿色 (#2ecc71) 并显示向上箭头 (↑)。
  ○ 下跌时，价格变为 红色 (#e74c3c) 并显示向下箭头 (↓)。
  ○ 无变化或首次加载时为默认颜色（如白色）。
● 状态显示：
  ○ 加载中： 显示 "..." 或一个平滑的 CSS 加载动画。
  ○ 错误： API 请求失败时，显示 "N/A" 或错误图标 (❗)，并在鼠标悬停时通过 tooltip 提示错误信息。
3.3. 数据获取
● API 接口： 使用模拟接口 https://api.example.com/gold/price，假定返回格式为 { "price": 1980.50 }。
● 更新机制：
  ○ 应用启动后立即获取一次价格。
  ○ 此后，每隔 30 秒 自动更新一次。
  ○ 这个更新间隔应是 可配置的（见系统集成部分）。
4. 系统集成
● 系统托盘图标 (System Tray Icon)：
  ○ 应用启动后，在系统的托盘区（Windows右下角、macOS菜单栏）显示一个应用图标。
  ○ 左键单击托盘图标：切换（显示/隐藏）悬浮球小组件。
  ○ 右键单击托盘图标：弹出一个上下文菜单，包含以下选项：
    ■ 显示/隐藏小组件
    ■ 设置... (打开设置窗口)
    ■ 退出应用
● 设置窗口 (Settings Window)：
  ○ 点击“设置”菜单项后，弹出一个独立的、标准的设置窗口。
  ○ 在此窗口中，用户可以配置：
    ■ 价格更新频率 (例如，输入框，单位为秒)。
    ■ （未来扩展）选择不同的货币或商品。
● 应用退出：
  ○ 只有通过托盘菜单的“退出应用”选项才能完全关闭程序。关闭主窗口（如果可关闭）应仅隐藏它。
5. 实现技术要点
● 进程模型： 清晰划分 主进程 (Main Process - in Electron) 和 渲染进程 (Renderer Process - Vue UI) 的职责。
  ○ 主进程负责： 创建和管理所有窗口（悬浮球、设置）、处理系统托盘、保存和读取配置文件、处理应用的生命周期。
  ○ 渲染进程负责： 渲染UI、发起API请求、管理UI状态。
● 构建与打包： 提供脚本，能将应用打包成适用于 Windows (.exe), macOS (.dmg/.app) 和 Linux (.AppImage) 的可执行文件。