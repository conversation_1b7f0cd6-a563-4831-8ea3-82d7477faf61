import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [vue()],
  root: 'src/renderer',
  base: './',
  build: {
    outDir: '../../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src/renderer/index.html'),
        settings: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src/renderer/settings.html')
      }
    }
  },
  server: {
    port: 5173
  }
})
