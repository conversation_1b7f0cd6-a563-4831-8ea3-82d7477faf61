import { ref, reactive } from 'vue'
import axios from 'axios'

export function useGoldPrice() {
  const price = ref(null)
  const previousPrice = ref(null)
  const priceChange = ref(null) // 'up', 'down', or null
  const isLoading = ref(false)
  const hasError = ref(false)
  const errorMessage = ref('')
  const updateInterval = ref(30000) // 30 seconds default
  
  let intervalId = null

  // Mock API endpoint - in real implementation, this would be a real gold price API
  const API_ENDPOINT = 'https://api.example.com/gold/price'

  // For development, we'll use a mock function that simulates price changes
  const fetchPriceFromAPI = async () => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Simulate price fluctuation around $1980
    const basePrice = 1980
    const variation = (Math.random() - 0.5) * 20 // ±$10 variation
    const newPrice = basePrice + variation
    
    // Simulate occasional API errors (5% chance)
    if (Math.random() < 0.05) {
      throw new Error('API temporarily unavailable')
    }
    
    return {
      price: parseFloat(newPrice.toFixed(2))
    }
  }

  const fetchPrice = async () => {
    isLoading.value = true
    hasError.value = false
    errorMessage.value = ''

    try {
      // Store previous price for comparison
      if (price.value !== null) {
        previousPrice.value = price.value
      }

      // Try to fetch from real API first, fallback to mock
      let response
      try {
        response = await axios.get(API_ENDPOINT, { timeout: 5000 })
      } catch (apiError) {
        console.log('Real API not available, using mock data')
        response = { data: await fetchPriceFromAPI() }
      }

      const newPrice = response.data.price
      
      if (typeof newPrice === 'number' && newPrice > 0) {
        price.value = newPrice
        
        // Determine price change direction
        if (previousPrice.value !== null) {
          if (newPrice > previousPrice.value) {
            priceChange.value = 'up'
          } else if (newPrice < previousPrice.value) {
            priceChange.value = 'down'
          } else {
            priceChange.value = null
          }
        }
      } else {
        throw new Error('Invalid price data received')
      }

    } catch (error) {
      console.error('Error fetching gold price:', error)
      hasError.value = true
      errorMessage.value = error.message || 'Failed to fetch price'
      priceChange.value = null
    } finally {
      isLoading.value = false
    }
  }

  const startAutoUpdate = async () => {
    // Get update interval from config if available
    try {
      if (window.electronAPI) {
        const config = await window.electronAPI.getConfig()
        updateInterval.value = config.updateInterval || 30000
      }
    } catch (error) {
      console.log('Could not load config, using default interval')
    }

    // Clear any existing interval
    stopAutoUpdate()
    
    // Set up new interval
    intervalId = setInterval(fetchPrice, updateInterval.value)
  }

  const stopAutoUpdate = () => {
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
  }

  const updateConfig = async (newConfig) => {
    try {
      if (window.electronAPI) {
        await window.electronAPI.saveConfig(newConfig)
        
        // Update interval if changed
        if (newConfig.updateInterval && newConfig.updateInterval !== updateInterval.value) {
          updateInterval.value = newConfig.updateInterval
          
          // Restart auto-update with new interval
          if (intervalId) {
            stopAutoUpdate()
            startAutoUpdate()
          }
        }
      }
    } catch (error) {
      console.error('Error updating config:', error)
    }
  }

  return {
    price,
    previousPrice,
    priceChange,
    isLoading,
    hasError,
    errorMessage,
    updateInterval,
    fetchPrice,
    startAutoUpdate,
    stopAutoUpdate,
    updateConfig
  }
}
