{"name": "gold-price-widget", "version": "1.0.0", "description": "A cross-platform desktop widget for real-time gold price monitoring", "main": "src/main/main.js", "scripts": {"dev": "vite", "build": "vite build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux"}, "keywords": ["electron", "vue", "gold", "price", "widget", "desktop"], "author": "Gold Price Widget", "license": "MIT", "dependencies": {"axios": "^1.10.0", "electron": "^37.1.0", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "concurrently": "^9.2.0", "electron-builder": "^26.0.12", "vite": "^5.4.19", "wait-on": "^8.0.3"}, "build": {"appId": "com.goldpricewidget.app", "productName": "Gold Price Widget", "directories": {"output": "dist"}, "files": ["src/main/**/*", "dist/**/*", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}