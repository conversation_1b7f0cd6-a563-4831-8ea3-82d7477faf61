// Settings page functionality
document.addEventListener('DOMContentLoaded', async () => {
  const form = document.getElementById('settingsForm')
  const updateIntervalInput = document.getElementById('updateInterval')
  const cancelBtn = document.getElementById('cancelBtn')
  const saveBtn = document.getElementById('saveBtn')
  const statusMessage = document.getElementById('statusMessage')

  // Load current configuration
  async function loadConfig() {
    try {
      if (window.electronAPI) {
        const config = await window.electronAPI.getConfig()
        updateIntervalInput.value = (config.updateInterval || 30000) / 1000 // Convert to seconds
      }
    } catch (error) {
      console.error('Error loading config:', error)
      showStatus('Error loading settings', 'error')
    }
  }

  // Show status message
  function showStatus(message, type = 'success') {
    statusMessage.textContent = message
    statusMessage.className = `status-message status-${type}`
    statusMessage.style.display = 'block'
    
    // Hide after 3 seconds
    setTimeout(() => {
      statusMessage.style.display = 'none'
    }, 3000)
  }

  // Save configuration
  async function saveConfig() {
    try {
      const updateInterval = parseInt(updateIntervalInput.value) * 1000 // Convert to milliseconds
      
      // Validate input
      if (updateInterval < 5000 || updateInterval > 300000) {
        showStatus('Update interval must be between 5 and 300 seconds', 'error')
        return
      }

      if (window.electronAPI) {
        await window.electronAPI.saveConfig({
          updateInterval: updateInterval
        })
        showStatus('Settings saved successfully!', 'success')
      } else {
        showStatus('Settings saved locally', 'success')
      }
    } catch (error) {
      console.error('Error saving config:', error)
      showStatus('Error saving settings', 'error')
    }
  }

  // Event listeners
  form.addEventListener('submit', (e) => {
    e.preventDefault()
    saveConfig()
  })

  cancelBtn.addEventListener('click', () => {
    // Close the settings window
    if (window.electronAPI && window.electronAPI.close) {
      window.electronAPI.close()
    } else {
      window.close()
    }
  })

  // Load initial configuration
  await loadConfig()
})
