import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",async()=>{const o=document.getElementById("settingsForm"),s=document.getElementById("updateInterval"),r=document.getElementById("cancelBtn");document.getElementById("saveBtn");const n=document.getElementById("statusMessage");async function a(){try{if(window.electronAPI){const e=await window.electronAPI.getConfig();s.value=(e.updateInterval||3e4)/1e3}}catch(e){console.error("Error loading config:",e),t("Error loading settings","error")}}function t(e,l="success"){n.textContent=e,n.className=`status-message status-${l}`,n.style.display="block",setTimeout(()=>{n.style.display="none"},3e3)}async function c(){try{const e=parseInt(s.value)*1e3;if(e<5e3||e>3e5){t("Update interval must be between 5 and 300 seconds","error");return}window.electronAPI?(await window.electronAPI.saveConfig({updateInterval:e}),t("Settings saved successfully!","success")):t("Settings saved locally","success")}catch(e){console.error("Error saving config:",e),t("Error saving settings","error")}}o.addEventListener("submit",e=>{e.preventDefault(),c()}),r.addEventListener("click",()=>{window.electronAPI&&window.electronAPI.close?window.electronAPI.close():window.close()}),await a()});
